openapi: 3.0.3
info:
  title: OneFoodDialer Consolidated API
  version: 1.0.0
  description: Consolidated contract combining QuickServe and Customer Service APIs.
  contact: { name: API Team, email: <EMAIL> }
servers:
  - url: https://api.onefooddialer.com
    description: Production
  - url: http://localhost:8000
    description: Local

security:
  - bearerAuth: []

tags:
  - name: QuickServe
  - name: CustomerService

paths:
  /orders:
    $ref: ./services/quickserve-service-v12/openapi.yaml#/paths/~1orders
  /orders/{id}:
    $ref: ./services/quickserve-service-v12/openapi.yaml#/paths/~1orders~1{id}
  /customers:
    $ref: ./services/customer-service-v12/openapi-customer-service.yaml#/paths/~1customers
  /customers/{id}:
    $ref: ./services/customer-service-v12/openapi-customer-service.yaml#/paths/~1customers~1{id}

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    QuickServe_Order:
      $ref: ./services/quickserve-service-v12/openapi.yaml#/components/schemas/Order
    Customer_User:
      $ref: ./services/customer-service-v12/openapi-customer-service.yaml#/components/schemas/Customer
    Error:
      type: object
      required: [code, message]
      properties:
        code: { type: string }
        message: { type: string }
        details: { type: object }

# Source OpenAPI files included in this contract (for reference)
x-imports:
  - services/quickserve-service-v12/order-management-openapi.yaml
  - services/quickserve-service-v12/openapi.yaml
  - services/customer-service-v12/openapi-customer-service.yaml
  - services/auth-service-v12/openapi-auth-service.yaml
  - services/admin-service-v12/openapi.yaml
  - services/catalogue-service-v12/openapi.yaml
  - services/payment-service-v12/openapi.yaml
