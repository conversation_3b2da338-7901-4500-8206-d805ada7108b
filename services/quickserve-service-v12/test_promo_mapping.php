<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Promo Code Mapping Test ===\n\n";

// Test 1: Check current mapping logic
echo "Test 1: Current mapping logic\n";
echo "------------------------------\n";

// Simulate the current logic
$validated = [];

// Scenario 1: Manual promo code
$validated['manual_promo_code'] = 'SPECIAL';  // User entered promo code
$validated['system_promo_code'] = null;       // No plan-linked promo

echo "Scenario 1: Manual promo code 'SPECIAL'\n";
echo "  \$validated['manual_promo_code'] = '{$validated['manual_promo_code']}'\n";
echo "  \$validated['system_promo_code'] = " . ($validated['system_promo_code'] ?? 'NULL') . "\n";
echo "  Database mapping:\n";
echo "    promo_code = " . ($validated['system_promo_code'] ?? 'NULL') . " (should be NULL for manual promo)\n";
echo "    system_promo_code = '{$validated['manual_promo_code']}' (should contain manual promo)\n";
echo "  Expected result: Manual promo 'SPECIAL' should go to system_promo_code column ✓\n\n";

// Scenario 2: Plan-linked promo code
$validated['manual_promo_code'] = null;
$validated['system_promo_code'] = 'PLAN10';  // Plan-linked promo code

echo "Scenario 2: Plan-linked promo code 'PLAN10'\n";
echo "  \$validated['manual_promo_code'] = " . ($validated['manual_promo_code'] ?? 'NULL') . "\n";
echo "  \$validated['system_promo_code'] = '{$validated['system_promo_code']}'\n";
echo "  Database mapping:\n";
echo "    promo_code = '{$validated['system_promo_code']}' (should contain plan-linked promo)\n";
echo "    system_promo_code = " . ($validated['manual_promo_code'] ?? 'NULL') . " (should be NULL for plan promo)\n";
echo "  Expected result: Plan promo 'PLAN10' should go to promo_code column ✓\n\n";

// Test 2: Check actual database data
echo "Test 2: Actual database data\n";
echo "-----------------------------\n";

try {
    $recentOrders = DB::table('temp_pre_orders')
        ->where(function($query) {
            $query->whereNotNull('promo_code')
                  ->orWhereNotNull('system_promo_code');
        })
        ->orderBy('last_modified', 'desc')
        ->limit(5)
        ->get(['order_no', 'promo_code', 'system_promo_code', 'applied_discount']);

    if ($recentOrders->count() > 0) {
        echo "Recent orders with promo codes:\n";
        foreach ($recentOrders as $order) {
            echo "  Order: {$order->order_no}\n";
            echo "    promo_code: " . ($order->promo_code ?? 'NULL') . "\n";
            echo "    system_promo_code: " . ($order->system_promo_code ?? 'NULL') . "\n";
            echo "    applied_discount: {$order->applied_discount}\n";
            
            // Analyze the mapping
            if ($order->promo_code && !$order->system_promo_code) {
                echo "    Analysis: Promo code in promo_code column (should be plan-linked)\n";
            } elseif (!$order->promo_code && $order->system_promo_code) {
                echo "    Analysis: Promo code in system_promo_code column (should be manual)\n";
            } elseif ($order->promo_code && $order->system_promo_code) {
                echo "    Analysis: Both columns have values (double promo scenario)\n";
            }
            echo "\n";
        }
    } else {
        echo "No recent orders with promo codes found.\n";
    }

} catch (Exception $e) {
    echo "Error querying database: " . $e->getMessage() . "\n";
}

// Test 3: Check if SPECIAL is plan-linked or manual
echo "Test 3: Check SPECIAL promo code type\n";
echo "-------------------------------------\n";

try {
    $specialPromo = DB::table('promo_codes')
        ->where('promo_code', 'SPECIAL')
        ->first(['pk_promo_code', 'promo_code', 'discount_type', 'amount']);

    if ($specialPromo) {
        echo "SPECIAL promo code found:\n";
        echo "  pk_promo_code: {$specialPromo->pk_promo_code}\n";
        echo "  discount_type: {$specialPromo->discount_type}\n";
        echo "  amount: {$specialPromo->amount}\n";

        // Check if it's linked to any plan
        $linkedPlans = DB::table('plan_master')
            ->where('fk_promo_code', $specialPromo->pk_promo_code)
            ->count();

        if ($linkedPlans > 0) {
            echo "  Type: PLAN-LINKED (linked to {$linkedPlans} plan(s))\n";
            echo "  Should go to: promo_code column\n";
        } else {
            echo "  Type: MANUAL (not linked to any plan)\n";
            echo "  Should go to: system_promo_code column\n";
        }
    } else {
        echo "SPECIAL promo code not found in database.\n";
    }

} catch (Exception $e) {
    echo "Error checking SPECIAL promo: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
