<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Support\Facades\DB;

/**
 * OrderQueryService
 * Single-responsibility: fast, paginated, minimal-shape order reads.
 */
class OrderQueryService
{
    /**
     * Fetch paginated orders (basic projection) for a customer.
     * Returns array with 'data','pagination'.
     */
    public function fetchCustomerOrders(int $customerId, int $companyId, int $page = 1, int $perPage = 10, bool $includeCancelled = false): array
    {
        $base = DB::table('orders')
            ->select([
                'pk_order_no as id',
                'order_no',
                'order_date',
                'order_status',
                'delivery_status',
                'product_name',
                'amount',
                'tax',
                DB::raw('(COALESCE(amount,0)+COALESCE(tax,0)) as gross_total'),
                'order_menu as meal_type'
            ])
            ->where('customer_code', $customerId)
            ->where('company_id', $companyId);

        if (!$includeCancelled) {
            $base->whereNotIn('order_status', ['Cancelled','Cancel']);
        }

        $total = (clone $base)->count();
        $rows = $base
            ->orderByDesc('order_date')
            ->orderByDesc('pk_order_no')
            ->forPage($page, $perPage)
            ->get();

        return [
            'data' => $rows->toArray(),
            'pagination' => [
                'page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => (int) ceil($total / max(1, $perPage))
            ]
        ];
    }

    /**
     * Distinct student names (derived from customer_address parsing in existing orders) in separate light query.
     */
    public function fetchStudentNames(int $customerId, int $companyId, bool $includeCancelled = false): array
    {
        $q = DB::table('orders')
            ->select('ship_address')
            ->where('customer_code', $customerId)
            ->where('company_id', $companyId);

        if (!$includeCancelled) {
            $q->whereNotIn('order_status', ['Cancelled','Cancel']);
        }

        $addresses = $q->limit(500)->pluck('ship_address')->filter();
        $names = [];
        foreach ($addresses as $addr) {
            $first = trim(strtok($addr, ',;|- '));
            if ($first !== '' && strlen($first) <= 50) {
                $names[$first] = true;
            }
        }
        return array_values(array_keys($names));
    }

    /**
     * Fetch orders and categorize (upcoming, cancelled, other) replicating legacy logic in a set-based way.
     * Returns structure: ['categories'=>[...], 'students'=>[...], 'pagination'=>[], 'timing_ms'=>float]
     * This avoids N+1 by precomputing meal items & plan type counts in bulk.
     */
    public function fetchCategorizedOrders(
        int $customerId,
        int $companyId,
        int $page = 1,
        int $perPage = 10,
        bool $includeCancelled = true,
        ?string $studentNameFilter = null
    ): array {
        $t0 = microtime(true);

        // Base query replicating main fields used by groupAndCategorizeOrders
        $base = DB::table('orders')
            ->select([
                'pk_order_no as order_id',
                'order_no',
                'order_date',
                'order_date as delivery_date', // same field in legacy
                'order_status',
                'delivery_status',
                'payment_mode',
                'amount_paid',
                DB::raw('COALESCE(amount,0)+COALESCE(tax,0)+COALESCE(delivery_charges,0)+COALESCE(service_charges,0) as total_amount'),
                'delivery_time',
                'delivery_end_time',
                'recurring_status',
                'days_preference',
                'ship_address as customer_address',
                'location_name',
                'city_name',
                'food_preference',
                'product_code',
                'product_name',
                'product_type',
                'quantity',
                DB::raw('COALESCE(amount,0) as item_amount'),
                DB::raw('updated_at as last_modified')
            ])
            ->where('customer_code', $customerId)
            ->where('company_id', $companyId);

        if (!$includeCancelled) {
            $base->whereNotIn('order_status', ['Cancelled','Cancel','Cancelled','Canceled']);
        }

    // We'll apply student_name_filter after row retrieval to mimic legacy first-token match.

        $total = (clone $base)->count();
        $rows = $base->orderByDesc('order_date')->orderByDesc('pk_order_no')->forPage($page, $perPage)->get();

        if ($rows->isEmpty()) {
            return [
                'categories' => ['upcoming'=>[], 'cancelled'=>[], 'other'=>[], 'all'=>[]],
                'students' => [],
                'pagination' => [
                    'page'=>$page,'per_page'=>$perPage,'total'=>$total,'total_pages'=>(int)ceil($total/max(1,$perPage))
                ],
                'timing_ms' => round((microtime(true)-$t0)*1000,2)
            ];
        }

        // Bulk derive plan type counts by order_no (single query)
        $orderNos = $rows->pluck('order_no')->unique()->values();
        $counts = DB::table('orders')
            ->select('order_no', DB::raw('COUNT(*) as c'))
            ->whereIn('order_no', $orderNos)
            ->groupBy('order_no')
            ->pluck('c','order_no');

        // Bulk meal items from order_details without per-row queries
        $detailMap = DB::table('order_details')
            ->select('ref_order_no as order_no','order_date', 'product_name')
            ->whereIn('ref_order_no', $orderNos)
            ->whereIn('order_date', $rows->pluck('order_date')->unique())
            ->get()
            ->groupBy(function($r){return $r->order_no.'|'.$r->order_date;});

        $today = date('Y-m-d');
        $categorized = [ 'upcoming'=>[], 'cancelled'=>[], 'other'=>[], 'all'=>[] ];
        $studentNamesIndex = [ 'upcoming'=>[], 'cancelled'=>[], 'other'=>[] ];

        foreach ($rows as $r) {
            $key = $r->order_no.'|'.$r->order_date;
            $mealItems = $detailMap->has($key) ? array_values(array_unique($detailMap[$key]->pluck('product_name')->toArray())) : [];
            $mealItemsCsv = implode(', ', $mealItems); // keep CSV for compatibility
            $planType = $this->planTypeFromCount((int)($counts[$r->order_no] ?? 1));
            $studentName = trim(strtok((string) $r->customer_address, ',;|- '));

            // Student name filter: skip if provided and not match (case-insensitive prefix)
            if ($studentNameFilter && stripos($studentName, $studentNameFilter) !== 0) {
                continue;
            }

            // Lightweight cancellation policy approximation (avoid heavy controller dependency)
            $isCancellable = $this->computeIsCancellable($r->delivery_date ?? $r->order_date, $r->order_status, $r->delivery_status);
            $cancellationPolicy = $this->buildCancellationPolicy($isCancellable);

            $record = [
                'order_id' => $r->order_id,
                'order_no' => $r->order_no,
                'order_date' => $r->order_date,
                'delivery_date' => $r->delivery_date,
                'order_status' => $r->order_status,
                'delivery_status' => $r->delivery_status,
                'payment_mode' => $r->payment_mode,
                'amount_paid' => $r->amount_paid,
                'total_amount' => (float) $r->total_amount,
                'delivery_time' => $r->delivery_time,
                'delivery_end_time' => $r->delivery_end_time,
                'recurring_status' => $r->recurring_status,
                'days_preference' => $r->days_preference,
                'customer_address' => $r->customer_address,
                'student_name' => $studentName,
                'location_name' => $r->location_name,
                'city_name' => $r->city_name,
                'food_preference' => $r->food_preference,
                'product_code' => $r->product_code,
                'product_name' => $r->product_name,
                'product_type' => $this->enhancedProductType($r->product_name, (int)$r->product_code),
                'original_product_type' => $r->product_type,
                'quantity' => $r->quantity,
                'item_amount' => (float) $r->item_amount,
                'last_modified' => $r->last_modified,
                'meal_items' => $mealItems, // array form
                'meal_items_csv' => $mealItemsCsv, // maintain CSV if caller expects it
                'plan_type' => $planType,
                'is_cancellable' => $isCancellable,
                'cancellation_policy' => $cancellationPolicy,
            ];

            $status = strtolower($r->order_status ?? '');
            $dStatus = strtolower($r->delivery_status ?? '');
            $cancelled = in_array($status, ['cancelled','canceled','cancel','refunded']) || in_array($dStatus, ['cancelled','canceled','cancel']);
            if ($cancelled) {
                $record['cancelled_on'] = $r->last_modified;
                $categorized['cancelled'][] = $record;
                if ($studentName !== '' && !in_array($studentName, $studentNamesIndex['cancelled'])) {
                    $studentNamesIndex['cancelled'][] = $studentName;
                }
            } elseif ($r->delivery_date && $r->delivery_date >= $today && !in_array($status, ['completed','delivered','complete']) && !in_array($dStatus, ['delivered','completed'])) {
                $categorized['upcoming'][] = $record;
                if ($studentName !== '' && !in_array($studentName, $studentNamesIndex['upcoming'])) {
                    $studentNamesIndex['upcoming'][] = $studentName;
                }
            } else {
                $categorized['other'][] = $record;
                if ($studentName !== '' && !in_array($studentName, $studentNamesIndex['other'])) {
                    $studentNamesIndex['other'][] = $studentName;
                }
            }
            $categorized['all'][] = $record;
        }

        // Sort categories to mirror original logic
        usort($categorized['upcoming'], fn($a,$b)=>strcmp($a['delivery_date'],$b['delivery_date']));
        usort($categorized['cancelled'], fn($a,$b)=>strcmp($b['delivery_date'],$a['delivery_date']));
        usort($categorized['other'], fn($a,$b)=>strcmp($b['delivery_date'],$a['delivery_date']));

        foreach ($studentNamesIndex as &$arr) { sort($arr); }

        return [
            'categories' => $categorized,
            'students' => $studentNamesIndex,
            'pagination' => [
                'page'=>$page,
                'per_page'=>$perPage,
                'total'=>$total,
                'total_pages'=>(int)ceil($total/max(1,$perPage))
            ],
            'timing_ms' => round((microtime(true)-$t0)*1000,2)
        ];
    }

    private function planTypeFromCount(int $count): string
    {
        if ($count === 1) {
            return 'single day';
        }
        return $count . ' day';
    }

    private function computeIsCancellable(?string $deliveryDate, ?string $orderStatus, ?string $deliveryStatus): bool
    {
        if (!$deliveryDate) {
            return false;
        }
        $today = date('Y-m-d');
        $status = strtolower($orderStatus ?? '');
        $dStatus = strtolower($deliveryStatus ?? '');
        if (in_array($status, ['cancelled','canceled','cancel','refunded']) || in_array($dStatus, ['cancelled','canceled','cancel'])) {
            return false;
        }
        // Simple rule: cancellable if delivery date is today or future and not delivered/completed
        if ($deliveryDate >= $today && !in_array($status, ['completed','delivered','complete']) && !in_array($dStatus, ['delivered','completed'])) {
            return true;
        }
        return false;
    }

    private function buildCancellationPolicy(bool $isCancellable): array
    {
        if (!$isCancellable) {
            return [
                'refund_percentage' => 0,
                'policy_type' => 'non_cancellable',
                'cutoff_time' => '00:00:00',
                'cutoff_day' => 0
            ];
        }
        // Placeholder static policy; could be enhanced to read settings similarly to original controller
        return [
            'refund_percentage' => 100,
            'policy_type' => 'standard',
            'cutoff_time' => '08:00:00',
            'cutoff_day' => 0
        ];
    }

    private function enhancedProductType(string $productName, int $productCode): string
    {
        $pn = strtolower($productName);
        $map = [
            339 => 'Breakfast', 342 => 'Breakfast', 343 => 'Breakfast', 597 => 'Breakfast',
            336 => 'Lunch', 338 => 'Lunch', 346 => 'Lunch', 598 => 'Lunch'
        ];
        $result = 'Meal';
        if (isset($map[$productCode])) {
            $result = $map[$productCode];
        } elseif (str_contains($pn, 'breakfast')) {
            $result = 'Breakfast';
        } elseif (str_contains($pn, 'lunch')) {
            $result = 'Lunch';
        } elseif (str_contains($pn, 'dinner')) {
            $result = 'Dinner';
        }
        return $result;
    }
}

