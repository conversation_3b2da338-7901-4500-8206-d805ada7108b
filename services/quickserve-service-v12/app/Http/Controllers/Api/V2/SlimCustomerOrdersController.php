<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Api\V2\BaseController;
use App\Services\OrderQueryService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Throwable;

/**
 * SlimCustomerOrdersController
 * New optimized endpoint: fast paginated order list + separate student names.
 * Reduces heavy logic and huge method count in OrderManagementController.
 */
class SlimCustomerOrdersController extends BaseController
{
    public function __construct(protected OrderQueryService $orders) {}

    /**
     * GET /api/v2/order-management/slim/customer/{customerId}
     * Query params: company_id (required), page, per_page, include_cancelled (bool)
     */
    public function list(Request $request, int $customerId): JsonResponse
    {
        $companyId = (int) $request->query('company_id', 0);
        if ($companyId <= 0) {
            return response()->json([
                'success' => false,
                'message' => 'company_id required'
            ], 422);
        }
        $page = max(1, (int) $request->query('page', 1));
        $perPage = min(50, max(1, (int) $request->query('per_page', 10))); // cap
    $includeCancelled = filter_var($request->query('include_cancelled', 'false'), FILTER_VALIDATE_BOOLEAN);
    $studentFilter = $request->query('student_name_filter');

        $t0 = microtime(true);
        try {
            $orders = $this->orders->fetchCustomerOrders($customerId, $companyId, $page, $perPage, $includeCancelled);
            $students = $this->orders->fetchStudentNames($customerId, $companyId, $includeCancelled);
            if ($studentFilter) {
                $orders['data'] = array_values(array_filter($orders['data'], function($row) use ($studentFilter) {
                    $addr = $row->ship_address ?? ($row->customer_address ?? '');
                    $first = trim(strtok((string)$addr, ',;|- '));
                    return stripos($first, $studentFilter) === 0;
                }));
            }
            $elapsed = round((microtime(true) - $t0) * 1000, 2);

            return response()->json([
                'success' => true,
                'timing_ms' => $elapsed,
                'orders' => $orders['data'],
                'pagination' => $orders['pagination'],
                'students' => $students,
            ]);
        } catch (Throwable $e) {
            Log::error('Slim customer orders failed', [
                'customer_id' => $customerId,
                'error' => $e->getMessage(),
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch orders'
            ], 500);
        }
    }

    /**
     * GET /api/v2/order-management/slim/customer/{customerId}/categorized
     * Mirrors original heavy endpoint structure (upcoming, cancelled, other, all + students) but optimized.
     * Query params: company_id, page, per_page, include_cancelled, student_name_filter
     */
    public function categorized(Request $request, int $customerId): JsonResponse
    {
        $companyId = (int) $request->query('company_id', 0);
        if ($companyId <= 0) {
            return response()->json([
                'success' => false,
                'message' => 'company_id required'
            ], 422);
        }
        $page = max(1, (int) $request->query('page', 1));
        $perPage = min(50, max(1, (int) $request->query('per_page', 10)));
        $includeCancelled = filter_var($request->query('include_cancelled', 'true'), FILTER_VALIDATE_BOOLEAN);
        $studentFilter = $request->query('student_name_filter');

        try {
            $result = $this->orders->fetchCategorizedOrders(
                $customerId,
                $companyId,
                $page,
                $perPage,
                $includeCancelled,
                $studentFilter
            );

            return response()->json([
                'success' => true,
                'timing_ms' => $result['timing_ms'],
                'upcoming' => $result['categories']['upcoming'],
                'cancelled' => $result['categories']['cancelled'],
                'other' => $result['categories']['other'],
                'all' => $result['categories']['all'],
                'students' => $result['students'],
                'pagination' => $result['pagination']
            ]);
        } catch (Throwable $e) {
            Log::error('Slim categorized orders failed', [
                'customer_id' => $customerId,
                'error' => $e->getMessage()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch categorized orders'
            ], 500);
        }
    }
}
