//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for MealCartItem
void main() {
  // final instance = MealCartItem();

  group('test MealCartItem', () {
    // Product code (details fetched from products table)
    // int productCode
    test('to test the property `productCode`', () async {
      // TODO
    });

    // Name of the product
    // String productName
    test('to test the property `productName`', () async {
      // TODO
    });

    // Quantity of this meal
    // int quantity
    test('to test the property `quantity`', () async {
      // TODO
    });

    // Price for this quantity of the product
    // double amount
    test('to test the property `amount`', () async {
      // TODO
    });


  });

}
