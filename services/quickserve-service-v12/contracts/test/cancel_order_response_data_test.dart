//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for CancelOrderResponseData
void main() {
  // final instance = CancelOrderResponseData();

  group('test CancelOrderResponseData', () {
    // Number of orders cancelled
    // int cancelledOrders
    test('to test the property `cancelledOrders`', () async {
      // TODO
    });

    // List of cancelled order IDs
    // List<int> cancelledOrderIds (default value: const [])
    test('to test the property `cancelledOrderIds`', () async {
      // TODO
    });

    // Total refund amount credited to wallet
    // num totalRefundAmount
    test('to test the property `totalRefundAmount`', () async {
      // TODO
    });

    // CancelOrderResponseDataCancellationDetails cancellationDetails
    test('to test the property `cancellationDetails`', () async {
      // TODO
    });


  });

}
