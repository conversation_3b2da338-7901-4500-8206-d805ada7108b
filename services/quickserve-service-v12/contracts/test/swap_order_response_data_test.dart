//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for SwapOrderResponseData
void main() {
  // final instance = SwapOrderResponseData();

  group('test SwapOrderResponseData', () {
    // Internal order ID
    // int orderId
    test('to test the property `orderId`', () async {
      // TODO
    });

    // Order number that was swapped
    // String orderNo
    test('to test the property `orderNo`', () async {
      // TODO
    });

    // Date of the swapped order
    // DateTime orderDate
    test('to test the property `orderDate`', () async {
      // TODO
    });

    // SwapOrderResponseDataSwapDetails swapDetails
    test('to test the property `swapDetails`', () async {
      // TODO
    });

    // Reason provided for the swap
    // String reason
    test('to test the property `reason`', () async {
      // TODO
    });


  });

}
