//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for ApplyCouponRequest
void main() {
  // final instance = ApplyCouponRequest();

  group('test ApplyCouponRequest', () {
    // Company id associated with this request
    // int companyId
    test('to test the property `companyId`', () async {
      // TODO
    });

    // Order number to apply coupon
    // String orderNo
    test('to test the property `orderNo`', () async {
      // TODO
    });

    // Promo code to apply
    // String promoCode
    test('to test the property `promoCode`', () async {
      // TODO
    });


  });

}
