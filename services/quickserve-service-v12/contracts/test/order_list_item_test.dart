//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for OrderListItem
void main() {
  // final instance = OrderListItem();

  group('test OrderListItem', () {
    // int orderId
    test('to test the property `orderId`', () async {
      // TODO
    });

    // String orderNo
    test('to test the property `orderNo`', () async {
      // TODO
    });

    // DateTime orderDate
    test('to test the property `orderDate`', () async {
      // TODO
    });

    // String orderStatus
    test('to test the property `orderStatus`', () async {
      // TODO
    });

    // String deliveryStatus
    test('to test the property `deliveryStatus`', () async {
      // TODO
    });

    // String paymentMode
    test('to test the property `paymentMode`', () async {
      // TODO
    });

    // num amountPaid
    test('to test the property `amountPaid`', () async {
      // TODO
    });

    // num totalAmount
    test('to test the property `totalAmount`', () async {
      // TODO
    });

    // String deliveryTime
    test('to test the property `deliveryTime`', () async {
      // TODO
    });

    // String deliveryEndTime
    test('to test the property `deliveryEndTime`', () async {
      // TODO
    });

    // int recurringStatus
    test('to test the property `recurringStatus`', () async {
      // TODO
    });

    // String daysPreference
    test('to test the property `daysPreference`', () async {
      // TODO
    });

    // String customerAddress
    test('to test the property `customerAddress`', () async {
      // TODO
    });

    // String locationName
    test('to test the property `locationName`', () async {
      // TODO
    });

    // String cityName
    test('to test the property `cityName`', () async {
      // TODO
    });

    // String foodPreference
    test('to test the property `foodPreference`', () async {
      // TODO
    });

    // int productCode
    test('to test the property `productCode`', () async {
      // TODO
    });

    // String productName
    test('to test the property `productName`', () async {
      // TODO
    });

    // String imagePath
    test('to test the property `imagePath`', () async {
      // TODO
    });

    // int quantity
    test('to test the property `quantity`', () async {
      // TODO
    });

    // num itemAmount
    test('to test the property `itemAmount`', () async {
      // TODO
    });

    // String productType
    test('to test the property `productType`', () async {
      // TODO
    });

    // DateTime lastModified
    test('to test the property `lastModified`', () async {
      // TODO
    });


  });

}
