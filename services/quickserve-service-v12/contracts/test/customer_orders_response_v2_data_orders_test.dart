//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for CustomerOrdersResponseV2DataOrders
void main() {
  // final instance = CustomerOrdersResponseV2DataOrders();

  group('test CustomerOrdersResponseV2DataOrders', () {
    // List<OrderListItem> upcoming (default value: const [])
    test('to test the property `upcoming`', () async {
      // TODO
    });

    // List<OrderListItem> cancelled (default value: const [])
    test('to test the property `cancelled`', () async {
      // TODO
    });

    // List<OrderListItem> other (default value: const [])
    test('to test the property `other`', () async {
      // TODO
    });


  });

}
