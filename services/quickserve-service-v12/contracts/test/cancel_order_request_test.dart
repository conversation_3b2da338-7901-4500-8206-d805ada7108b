//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for CancelOrderRequest
void main() {
  // final instance = CancelOrderRequest();

  group('test CancelOrderRequest', () {
    // Company id associated with this request
    // int companyId
    test('to test the property `companyId`', () async {
      // TODO
    });

    // Reason for order cancellation
    // String reason
    test('to test the property `reason`', () async {
      // TODO
    });

    // Specific dates to cancel
    // List<DateTime> cancelDates (default value: const [])
    test('to test the property `cancelDates`', () async {
      // TODO
    });

    // Optional: Filter cancellation by specific meal type
    // String mealType
    test('to test the property `mealType`', () async {
      // TODO
    });

    // Who initiated the cancellation
    // String cancelledBy
    test('to test the property `cancelledBy`', () async {
      // TODO
    });


  });

}
