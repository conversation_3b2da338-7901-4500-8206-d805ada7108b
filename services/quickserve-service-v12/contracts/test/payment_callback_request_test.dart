//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for PaymentCallbackRequest
void main() {
  // final instance = PaymentCallbackRequest();

  group('test PaymentCallbackRequest', () {
    // Company id associated with this callback
    // int companyId
    test('to test the property `companyId`', () async {
      // TODO
    });

    // Payment gateway transaction id
    // String transactionId
    test('to test the property `transactionId`', () async {
      // TODO
    });

    // Optional gateway response payload
    // Object gatewayResponse
    test('to test the property `gatewayResponse`', () async {
      // TODO
    });


  });

}
