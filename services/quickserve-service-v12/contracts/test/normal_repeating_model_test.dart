//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for NormalRepeatingModel
void main() {
  // final instance = NormalRepeatingModel();

  group('test NormalRepeatingModel', () {
    // List<MealCartItemShort> meals (default value: const [])
    test('to test the property `meals`', () async {
      // TODO
    });

    // DateTime startDate
    test('to test the property `startDate`', () async {
      // TODO
    });

    // List<int> selectedDays (default value: const [])
    test('to test the property `selectedDays`', () async {
      // TODO
    });

    // int subscriptionDays
    test('to test the property `subscriptionDays`', () async {
      // TODO
    });


  });

}
