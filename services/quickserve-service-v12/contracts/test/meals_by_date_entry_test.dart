//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for MealsByDateEntry
void main() {
  // final instance = MealsByDateEntry();

  group('test MealsByDateEntry', () {
    // Delivery date (YYYY-MM-DD), must be today or a future date.
    // DateTime date
    test('to test the property `date`', () async {
      // TODO
    });

    // List<PerDateMealItem> meals (default value: const [])
    test('to test the property `meals`', () async {
      // TODO
    });


  });

}
