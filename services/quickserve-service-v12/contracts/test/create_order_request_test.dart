//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for CreateOrderRequest
void main() {
  // final instance = CreateOrderRequest();

  group('test CreateOrderRequest', () {
    // int customerId
    test('to test the property `customerId`', () async {
      // TODO
    });

    // int userId
    test('to test the property `userId`', () async {
      // TODO
    });

    // int companyId
    test('to test the property `companyId`', () async {
      // TODO
    });

    // int unitId
    test('to test the property `unitId`', () async {
      // TODO
    });

    // int fkKitchenCode
    test('to test the property `fkKitchenCode`', () async {
      // TODO
    });

    // String customerName
    test('to test the property `customerName`', () async {
      // TODO
    });

    // String customerEmail
    test('to test the property `customerEmail`', () async {
      // TODO
    });

    // String customerPhone
    test('to test the property `customerPhone`', () async {
      // TODO
    });

    // String customerAddress
    test('to test the property `customerAddress`', () async {
      // TODO
    });

    // int locationCode
    test('to test the property `locationCode`', () async {
      // TODO
    });

    // String locationName
    test('to test the property `locationName`', () async {
      // TODO
    });

    // int city
    test('to test the property `city`', () async {
      // TODO
    });

    // String cityName
    test('to test the property `cityName`', () async {
      // TODO
    });

    // String foodPreference
    test('to test the property `foodPreference`', () async {
      // TODO
    });

    // Payment method: - online: Full payment via gateway - wallet: Use wallet balance (automatic partial payment if insufficient) 
    // String paymentMethod (default value: 'online')
    test('to test the property `paymentMethod`', () async {
      // TODO
    });

    // bool isExpress (default value: false)
    test('to test the property `isExpress`', () async {
      // TODO
    });

    // List<MealCartItemShort> meals (default value: const [])
    test('to test the property `meals`', () async {
      // TODO
    });

    // DateTime startDate
    test('to test the property `startDate`', () async {
      // TODO
    });

    // List<int> selectedDays (default value: const [])
    test('to test the property `selectedDays`', () async {
      // TODO
    });

    // int subscriptionDays
    test('to test the property `subscriptionDays`', () async {
      // TODO
    });

    // List<MealsByDateEntry> mealsByDate (default value: const [])
    test('to test the property `mealsByDate`', () async {
      // TODO
    });


  });

}
