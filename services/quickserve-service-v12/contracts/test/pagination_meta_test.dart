//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for PaginationMeta
void main() {
  // final instance = PaginationMeta();

  group('test PaginationMeta', () {
    // int currentPage
    test('to test the property `currentPage`', () async {
      // TODO
    });

    // int perPage
    test('to test the property `perPage`', () async {
      // TODO
    });

    // int total
    test('to test the property `total`', () async {
      // TODO
    });

    // int lastPage
    test('to test the property `lastPage`', () async {
      // TODO
    });

    // int from
    test('to test the property `from`', () async {
      // TODO
    });

    // int to
    test('to test the property `to`', () async {
      // TODO
    });

    // bool hasMore
    test('to test the property `hasMore`', () async {
      // TODO
    });


  });

}
