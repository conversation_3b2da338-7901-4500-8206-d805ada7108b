//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for CreateOrderResponseDataExpress
void main() {
  // final instance = CreateOrderResponseDataExpress();

  group('test CreateOrderResponseDataExpress', () {
    // bool evaluated
    test('to test the property `evaluated`', () async {
      // TODO
    });

    // bool withinWindow
    test('to test the property `withinWindow`', () async {
      // TODO
    });

    // String cutoffTime
    test('to test the property `cutoffTime`', () async {
      // TODO
    });

    // String extendedEndTime
    test('to test the property `extendedEndTime`', () async {
      // TODO
    });

    // num extraDeliveryCharge
    test('to test the property `extraDeliveryCharge`', () async {
      // TODO
    });


  });

}
