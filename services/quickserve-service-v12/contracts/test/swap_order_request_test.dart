//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for SwapOrderRequest
void main() {
  // final instance = SwapOrderRequest();

  group('test SwapOrderRequest', () {
    // Company id associated with this request
    // int companyId
    test('to test the property `companyId`', () async {
      // TODO
    });

    // Date of the order to swap (YYYY-MM-DD)
    // DateTime orderDate
    test('to test the property `orderDate`', () async {
      // TODO
    });

    // Product code of the new product to swap to
    // int newProductCode
    test('to test the property `newProductCode`', () async {
      // TODO
    });

    // Optional reason for the swap
    // String reason
    test('to test the property `reason`', () async {
      // TODO
    });

    // Optional filter by meal type
    // String mealType
    test('to test the property `mealType`', () async {
      // TODO
    });


  });

}
