//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for CustomerOrdersResponseV2DataFilters
void main() {
  // final instance = CustomerOrdersResponseV2DataFilters();

  group('test CustomerOrdersResponseV2DataFilters', () {
    // String studentNameFilter
    test('to test the property `studentNameFilter`', () async {
      // TODO
    });

    // bool includeCancelled
    test('to test the property `includeCancelled`', () async {
      // TODO
    });

    // String orderStatus
    test('to test the property `orderStatus`', () async {
      // TODO
    });

    // DateTime startDate
    test('to test the property `startDate`', () async {
      // TODO
    });

    // DateTime endDate
    test('to test the property `endDate`', () async {
      // TODO
    });

    // int perPage
    test('to test the property `perPage`', () async {
      // TODO
    });

    // int pageUpcoming
    test('to test the property `pageUpcoming`', () async {
      // TODO
    });

    // int pageCancelled
    test('to test the property `pageCancelled`', () async {
      // TODO
    });

    // int pageOther
    test('to test the property `pageOther`', () async {
      // TODO
    });


  });

}
