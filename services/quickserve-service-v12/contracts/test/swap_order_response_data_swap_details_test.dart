//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for SwapOrderResponseDataSwapDetails
void main() {
  // final instance = SwapOrderResponseDataSwapDetails();

  group('test SwapOrderResponseDataSwapDetails', () {
    // SwapOrderResponseDataSwapDetailsOldProduct oldProduct
    test('to test the property `oldProduct`', () async {
      // TODO
    });

    // SwapOrderResponseDataSwapDetailsNewProduct newProduct
    test('to test the property `newProduct`', () async {
      // TODO
    });

    // Price difference between old and new product
    // num priceDifference
    test('to test the property `priceDifference`', () async {
      // TODO
    });

    // Additional charges for the swap
    // num swapCharges
    test('to test the property `swapCharges`', () async {
      // TODO
    });

    // Total amount change (price difference + swap charges)
    // num totalAmountChange
    test('to test the property `totalAmountChange`', () async {
      // TODO
    });

    // New total order amount after swap
    // num newOrderAmount
    test('to test the property `newOrderAmount`', () async {
      // TODO
    });


  });

}
