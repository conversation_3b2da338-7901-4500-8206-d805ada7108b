//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for SwapOrderResponseDataSwapDetailsOldProduct
void main() {
  // final instance = SwapOrderResponseDataSwapDetailsOldProduct();

  group('test SwapOrderResponseDataSwapDetailsOldProduct', () {
    // int code
    test('to test the property `code`', () async {
      // TODO
    });

    // String name
    test('to test the property `name`', () async {
      // TODO
    });

    // num price
    test('to test the property `price`', () async {
      // TODO
    });


  });

}
