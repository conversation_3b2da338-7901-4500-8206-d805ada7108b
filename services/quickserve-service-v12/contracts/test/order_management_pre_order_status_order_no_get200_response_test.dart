//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for OrderManagementPreOrderStatusOrderNoGet200Response
void main() {
  // final instance = OrderManagementPreOrderStatusOrderNoGet200Response();

  group('test OrderManagementPreOrderStatusOrderNoGet200Response', () {
    // bool success
    test('to test the property `success`', () async {
      // TODO
    });

    // String status
    test('to test the property `status`', () async {
      // TODO
    });


  });

}
