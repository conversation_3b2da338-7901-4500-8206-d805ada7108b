//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for CustomerOrdersResponseV2DataSummary
void main() {
  // final instance = CustomerOrdersResponseV2DataSummary();

  group('test CustomerOrdersResponseV2DataSummary', () {
    // int totalOrders
    test('to test the property `totalOrders`', () async {
      // TODO
    });

    // int upcomingOrders
    test('to test the property `upcomingOrders`', () async {
      // TODO
    });

    // int cancelledOrders
    test('to test the property `cancelledOrders`', () async {
      // TODO
    });

    // int otherOrders
    test('to test the property `otherOrders`', () async {
      // TODO
    });


  });

}
