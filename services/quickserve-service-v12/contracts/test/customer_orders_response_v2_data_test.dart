//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for CustomerOrdersResponseV2Data
void main() {
  // final instance = CustomerOrdersResponseV2Data();

  group('test CustomerOrdersResponseV2Data', () {
    // CustomerOrdersResponseV2DataCustomer customer
    test('to test the property `customer`', () async {
      // TODO
    });

    // CustomerOrdersResponseV2DataSummary summary
    test('to test the property `summary`', () async {
      // TODO
    });

    // List<String> studentNames (default value: const [])
    test('to test the property `studentNames`', () async {
      // TODO
    });

    // CustomerOrdersResponseV2DataFilters filters
    test('to test the property `filters`', () async {
      // TODO
    });

    // CustomerOrdersResponseV2DataOrders orders
    test('to test the property `orders`', () async {
      // TODO
    });

    // CustomerOrdersResponseV2DataPagination pagination
    test('to test the property `pagination`', () async {
      // TODO
    });


  });

}
