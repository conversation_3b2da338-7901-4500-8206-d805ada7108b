# openapi.model.SwapOrderRequest

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**companyId** | **int** | Company id associated with this request | 
**orderDate** | [**DateTime**](DateTime.md) | Date of the order to swap (YYYY-MM-DD) | 
**newProductCode** | **int** | Product code of the new product to swap to | 
**reason** | **String** | Optional reason for the swap | [optional] 
**mealType** | **String** | Optional filter by meal type | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


