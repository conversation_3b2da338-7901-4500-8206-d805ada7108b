# openapi.model.MealCartItem

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**productCode** | **int** | Product code (details fetched from products table) | 
**productName** | **String** | Name of the product | 
**quantity** | **int** | Quantity of this meal | 
**amount** | **double** | Price for this quantity of the product | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


