# openapi.model.MealsByDateEntry

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**date** | [**DateTime**](DateTime.md) | Delivery date (YYYY-MM-DD), must be today or a future date. | 
**meals** | [**List<PerDateMealItem>**](PerDateMealItem.md) |  | [default to const []]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


