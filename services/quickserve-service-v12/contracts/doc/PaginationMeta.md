# openapi.model.PaginationMeta

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**currentPage** | **int** |  | [optional] 
**perPage** | **int** |  | [optional] 
**total** | **int** |  | [optional] 
**lastPage** | **int** |  | [optional] 
**from** | **int** |  | [optional] 
**to** | **int** |  | [optional] 
**hasMore** | **bool** |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


