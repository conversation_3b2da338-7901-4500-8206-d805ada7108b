# openapi.model.NormalRepeatingModel

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**meals** | [**List<MealCartItemShort>**](MealCartItemShort.md) |  | [default to const []]
**startDate** | [**DateTime**](DateTime.md) |  | 
**selectedDays** | **List<int>** |  | [default to const []]
**subscriptionDays** | **int** |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


