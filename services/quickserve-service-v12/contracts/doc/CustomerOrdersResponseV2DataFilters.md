# openapi.model.CustomerOrdersResponseV2DataFilters

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**studentNameFilter** | **String** |  | [optional] 
**includeCancelled** | **bool** |  | [optional] 
**orderStatus** | **String** |  | [optional] 
**startDate** | [**DateTime**](DateTime.md) |  | [optional] 
**endDate** | [**DateTime**](DateTime.md) |  | [optional] 
**perPage** | **int** |  | [optional] 
**pageUpcoming** | **int** |  | [optional] 
**pageCancelled** | **int** |  | [optional] 
**pageOther** | **int** |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


