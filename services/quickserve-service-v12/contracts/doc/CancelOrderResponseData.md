# openapi.model.CancelOrderResponseData

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**cancelledOrders** | **int** | Number of orders cancelled | [optional] 
**cancelledOrderIds** | **List<int>** | List of cancelled order IDs | [optional] [default to const []]
**totalRefundAmount** | **num** | Total refund amount credited to wallet | [optional] 
**cancellationDetails** | [**CancelOrderResponseDataCancellationDetails**](CancelOrderResponseDataCancellationDetails.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


