# openapi.model.OrderListItem

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**orderId** | **int** |  | [optional] 
**orderNo** | **String** |  | [optional] 
**orderDate** | [**DateTime**](DateTime.md) |  | [optional] 
**orderStatus** | **String** |  | [optional] 
**deliveryStatus** | **String** |  | [optional] 
**paymentMode** | **String** |  | [optional] 
**amountPaid** | **num** |  | [optional] 
**totalAmount** | **num** |  | [optional] 
**deliveryTime** | **String** |  | [optional] 
**deliveryEndTime** | **String** |  | [optional] 
**recurringStatus** | **int** |  | [optional] 
**daysPreference** | **String** |  | [optional] 
**customerAddress** | **String** |  | [optional] 
**locationName** | **String** |  | [optional] 
**cityName** | **String** |  | [optional] 
**foodPreference** | **String** |  | [optional] 
**productCode** | **int** |  | [optional] 
**productName** | **String** |  | [optional] 
**imagePath** | **String** |  | [optional] 
**quantity** | **int** |  | [optional] 
**itemAmount** | **num** |  | [optional] 
**productType** | **String** |  | [optional] 
**lastModified** | [**DateTime**](DateTime.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


