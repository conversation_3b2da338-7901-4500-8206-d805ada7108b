# openapi.model.OrderManagementCreatePostRequest

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**customerId** | **int** |  | 
**userId** | **int** |  | 
**companyId** | **int** |  | 
**unitId** | **int** |  | 
**fkKitchenCode** | **int** |  | 
**customerName** | **String** |  | [optional] 
**customerEmail** | **String** |  | [optional] 
**customerPhone** | **String** |  | [optional] 
**customerAddress** | **String** |  | 
**locationCode** | **int** |  | 
**locationName** | **String** |  | 
**city** | **int** |  | 
**cityName** | **String** |  | 
**foodPreference** | **String** |  | [optional] 
**paymentMethod** | **String** | Payment method: - online: Full payment via gateway - wallet: Use wallet balance (automatic partial payment if insufficient)  | [optional] [default to 'online']
**isExpress** | **bool** |  | 
**meals** | [**List<MealCartItemShort>**](MealCartItemShort.md) |  | [default to const []]
**startDate** | [**DateTime**](DateTime.md) |  | 
**selectedDays** | **List<int>** |  | [default to const []]
**subscriptionDays** | **int** |  | 
**mealsByDate** | [**List<MealsByDateEntry>**](MealsByDateEntry.md) |  | [default to const []]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


