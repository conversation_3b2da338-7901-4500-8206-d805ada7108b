# openapi.api.OrderManagementApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://************:8000/api/v2*

Method | HTTP request | Description
------------- | ------------- | -------------
[**orderManagementApplyCouponPost**](OrderManagementApi.md#ordermanagementapplycouponpost) | **POST** /order-management/apply-coupon | Apply coupon to pre-order or order
[**orderManagementCancelOrderNoPost**](OrderManagementApi.md#ordermanagementcancelordernopost) | **POST** /order-management/cancel/{orderNo} | Cancel order with time-based refund processing
[**orderManagementCreatePost**](OrderManagementApi.md#ordermanagementcreatepost) | **POST** /order-management/create | Create new order with payment integration (Normal and Express)
[**orderManagementCustomerCustomerCodeGet**](OrderManagementApi.md#ordermanagementcustomercustomercodeget) | **GET** /order-management/customer/{customerCode} | Get customer orders with cancellation details
[**orderManagementDetailsOrderNoGet**](OrderManagementApi.md#ordermanagementdetailsordernoget) | **GET** /order-management/details/{orderNo} | Get order details with payment status
[**orderManagementPaymentFailureOrderNoPost**](OrderManagementApi.md#ordermanagementpaymentfailureordernopost) | **POST** /order-management/payment-failure/{orderNo} | Payment failure callback from payment service
[**orderManagementPaymentSuccessOrderNoPost**](OrderManagementApi.md#ordermanagementpaymentsuccessordernopost) | **POST** /order-management/payment-success/{orderNo} | Payment success callback from payment service
[**orderManagementPreOrderStatusOrderNoGet**](OrderManagementApi.md#ordermanagementpreorderstatusordernoget) | **GET** /order-management/pre-order-status/{orderNo} | Check pre-order status for a given order number
[**orderManagementSwapOrderNoPost**](OrderManagementApi.md#ordermanagementswapordernopost) | **POST** /order-management/swap/{orderNo} | Swap order product with another product from the same category


# **orderManagementApplyCouponPost**
> OrderManagementApplyCouponPost200Response orderManagementApplyCouponPost(applyCouponRequest)

Apply coupon to pre-order or order

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: BearerAuth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('BearerAuth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('BearerAuth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = OrderManagementApi();
final applyCouponRequest = ApplyCouponRequest(); // ApplyCouponRequest | 

try {
    final result = api_instance.orderManagementApplyCouponPost(applyCouponRequest);
    print(result);
} catch (e) {
    print('Exception when calling OrderManagementApi->orderManagementApplyCouponPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **applyCouponRequest** | [**ApplyCouponRequest**](ApplyCouponRequest.md)|  | 

### Return type

[**OrderManagementApplyCouponPost200Response**](OrderManagementApplyCouponPost200Response.md)

### Authorization

[BearerAuth](../README.md#BearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **orderManagementCancelOrderNoPost**
> CancelOrderResponse orderManagementCancelOrderNoPost(orderNo, cancelOrderRequest)

Cancel order with time-based refund processing

Advanced order cancellation with time-based refund policies and wallet management. Includes cancellation tracking with user details and timestamps. 

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: BearerAuth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('BearerAuth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('BearerAuth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = OrderManagementApi();
final orderNo = QA93250725; // String | Order number to cancel
final cancelOrderRequest = CancelOrderRequest(); // CancelOrderRequest | 

try {
    final result = api_instance.orderManagementCancelOrderNoPost(orderNo, cancelOrderRequest);
    print(result);
} catch (e) {
    print('Exception when calling OrderManagementApi->orderManagementCancelOrderNoPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **orderNo** | **String**| Order number to cancel | 
 **cancelOrderRequest** | [**CancelOrderRequest**](CancelOrderRequest.md)|  | 

### Return type

[**CancelOrderResponse**](CancelOrderResponse.md)

### Authorization

[BearerAuth](../README.md#BearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **orderManagementCreatePost**
> CreateOrderResponse orderManagementCreatePost(orderManagementCreatePostRequest)

Create new order with payment integration (Normal and Express)

Creates a new order with temporary tables and payment integration. Returns payment_service_transaction_id for mobile app payment processing.  Normal Orders: - Supports two models:   1) Repeating model using `start_date`, `selected_days`, `subscription_days`, and a single `meals` cart that repeats.   2) Per-date heterogeneous model using `meals_by_date` to schedule different meals per specific date.  Express Orders: - Set `is_express=true`. System evaluates meal-specific cutoff. - If DB cutoff is `00:00:00` (midnight), same-day express allowed until `08:00:00` (configurable) with extra delivery charge. - Extra delivery charge is per kitchen and meal via setting `K{KITCHEN_ID}_{MEALTYPE}_EXPRESS_EXTRA_DELIVERY_CHARGE`. 

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: BearerAuth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('BearerAuth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('BearerAuth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = OrderManagementApi();
final orderManagementCreatePostRequest = OrderManagementCreatePostRequest(); // OrderManagementCreatePostRequest | 

try {
    final result = api_instance.orderManagementCreatePost(orderManagementCreatePostRequest);
    print(result);
} catch (e) {
    print('Exception when calling OrderManagementApi->orderManagementCreatePost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **orderManagementCreatePostRequest** | [**OrderManagementCreatePostRequest**](OrderManagementCreatePostRequest.md)|  | 

### Return type

[**CreateOrderResponse**](CreateOrderResponse.md)

### Authorization

[BearerAuth](../README.md#BearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **orderManagementCustomerCustomerCodeGet**
> CustomerOrdersResponseV2 orderManagementCustomerCustomerCodeGet(customerCode, companyId, includeCancelled, studentNameFilter, orderStatus, startDate, endDate, perPage, page)

Get customer orders with cancellation details

Retrieves all orders for a specific customer including cancellation details such as cancelled by whom and cancellation date. 

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: BearerAuth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('BearerAuth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('BearerAuth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = OrderManagementApi();
final customerCode = 1; // int | Customer code
final companyId = 56; // int | Company id (required) - must be provided as query parameter for GET requests
final includeCancelled = true; // bool | Include cancelled orders in response (accepts true/false/1/0/on/off)
final studentNameFilter = Aarav; // String | Filter orders where ship_address starts with this value (student name prefix)
final orderStatus = orderStatus_example; // String | Filter by order status
final startDate = 2025-01-01; // DateTime | Inclusive start date (YYYY-MM-DD) for filtering by order_date
final endDate = 2025-01-31; // DateTime | Inclusive end date (YYYY-MM-DD) for filtering by order_date
final perPage = 56; // int | Page size for the combined orders list (\"all\")
final page = 56; // int | Page number for the combined orders list (\"all\")

try {
    final result = api_instance.orderManagementCustomerCustomerCodeGet(customerCode, companyId, includeCancelled, studentNameFilter, orderStatus, startDate, endDate, perPage, page);
    print(result);
} catch (e) {
    print('Exception when calling OrderManagementApi->orderManagementCustomerCustomerCodeGet: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **customerCode** | **int**| Customer code | 
 **companyId** | **int**| Company id (required) - must be provided as query parameter for GET requests | 
 **includeCancelled** | **bool**| Include cancelled orders in response (accepts true/false/1/0/on/off) | [optional] [default to false]
 **studentNameFilter** | **String**| Filter orders where ship_address starts with this value (student name prefix) | [optional] 
 **orderStatus** | **String**| Filter by order status | [optional] 
 **startDate** | **DateTime**| Inclusive start date (YYYY-MM-DD) for filtering by order_date | [optional] 
 **endDate** | **DateTime**| Inclusive end date (YYYY-MM-DD) for filtering by order_date | [optional] 
 **perPage** | **int**| Page size for the combined orders list (\"all\") | [optional] [default to 10]
 **page** | **int**| Page number for the combined orders list (\"all\") | [optional] [default to 1]

### Return type

[**CustomerOrdersResponseV2**](CustomerOrdersResponseV2.md)

### Authorization

[BearerAuth](../README.md#BearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **orderManagementDetailsOrderNoGet**
> CreateOrderResponse orderManagementDetailsOrderNoGet(orderNo, companyId)

Get order details with payment status

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: BearerAuth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('BearerAuth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('BearerAuth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = OrderManagementApi();
final orderNo = QA93250725; // String | Order number to retrieve details for
final companyId = 56; // int | Company id (required) - must be provided as query parameter for GET requests

try {
    final result = api_instance.orderManagementDetailsOrderNoGet(orderNo, companyId);
    print(result);
} catch (e) {
    print('Exception when calling OrderManagementApi->orderManagementDetailsOrderNoGet: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **orderNo** | **String**| Order number to retrieve details for | 
 **companyId** | **int**| Company id (required) - must be provided as query parameter for GET requests | 

### Return type

[**CreateOrderResponse**](CreateOrderResponse.md)

### Authorization

[BearerAuth](../README.md#BearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **orderManagementPaymentFailureOrderNoPost**
> ErrorResponse orderManagementPaymentFailureOrderNoPost(orderNo, paymentCallbackRequest)

Payment failure callback from payment service

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: BearerAuth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('BearerAuth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('BearerAuth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = OrderManagementApi();
final orderNo = QA93250725; // String | Order number for payment callback
final paymentCallbackRequest = PaymentCallbackRequest(); // PaymentCallbackRequest | 

try {
    final result = api_instance.orderManagementPaymentFailureOrderNoPost(orderNo, paymentCallbackRequest);
    print(result);
} catch (e) {
    print('Exception when calling OrderManagementApi->orderManagementPaymentFailureOrderNoPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **orderNo** | **String**| Order number for payment callback | 
 **paymentCallbackRequest** | [**PaymentCallbackRequest**](PaymentCallbackRequest.md)|  | 

### Return type

[**ErrorResponse**](ErrorResponse.md)

### Authorization

[BearerAuth](../README.md#BearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **orderManagementPaymentSuccessOrderNoPost**
> ErrorResponse orderManagementPaymentSuccessOrderNoPost(orderNo, paymentCallbackRequest)

Payment success callback from payment service

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: BearerAuth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('BearerAuth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('BearerAuth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = OrderManagementApi();
final orderNo = QA93250725; // String | Order number for payment callback
final paymentCallbackRequest = PaymentCallbackRequest(); // PaymentCallbackRequest | 

try {
    final result = api_instance.orderManagementPaymentSuccessOrderNoPost(orderNo, paymentCallbackRequest);
    print(result);
} catch (e) {
    print('Exception when calling OrderManagementApi->orderManagementPaymentSuccessOrderNoPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **orderNo** | **String**| Order number for payment callback | 
 **paymentCallbackRequest** | [**PaymentCallbackRequest**](PaymentCallbackRequest.md)|  | 

### Return type

[**ErrorResponse**](ErrorResponse.md)

### Authorization

[BearerAuth](../README.md#BearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **orderManagementPreOrderStatusOrderNoGet**
> OrderManagementPreOrderStatusOrderNoGet200Response orderManagementPreOrderStatusOrderNoGet(orderNo, companyId)

Check pre-order status for a given order number

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: BearerAuth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('BearerAuth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('BearerAuth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = OrderManagementApi();
final orderNo = QA93250725; // String | Pre-order number to check status
final companyId = 56; // int | Company id (required) - must be provided as query parameter for GET requests

try {
    final result = api_instance.orderManagementPreOrderStatusOrderNoGet(orderNo, companyId);
    print(result);
} catch (e) {
    print('Exception when calling OrderManagementApi->orderManagementPreOrderStatusOrderNoGet: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **orderNo** | **String**| Pre-order number to check status | 
 **companyId** | **int**| Company id (required) - must be provided as query parameter for GET requests | 

### Return type

[**OrderManagementPreOrderStatusOrderNoGet200Response**](OrderManagementPreOrderStatusOrderNoGet200Response.md)

### Authorization

[BearerAuth](../README.md#BearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **orderManagementSwapOrderNoPost**
> SwapOrderResponse orderManagementSwapOrderNoPost(orderNo, swapOrderRequest)

Swap order product with another product from the same category

Allows customers to swap their meal with another product from the same category. Updates both orders and order_details tables while maintaining data integrity.  **Key Features:** - Product category validation - Only same category swaps allowed - Order status validation - Only swappable orders (not delivered/cancelled) - Price difference calculation - Automatic price adjustment - Swap charges support - Additional charges for premium swaps - Tax recalculation - Updated tax based on new amount - Audit logging - Complete swap history tracking  **Price Calculation:** ``` New Amount = Old Amount + Price Difference + Swap Charges ``` 

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: BearerAuth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('BearerAuth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('BearerAuth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = OrderManagementApi();
final orderNo = QA93250725; // String | Order number to swap
final swapOrderRequest = SwapOrderRequest(); // SwapOrderRequest | 

try {
    final result = api_instance.orderManagementSwapOrderNoPost(orderNo, swapOrderRequest);
    print(result);
} catch (e) {
    print('Exception when calling OrderManagementApi->orderManagementSwapOrderNoPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **orderNo** | **String**| Order number to swap | 
 **swapOrderRequest** | [**SwapOrderRequest**](SwapOrderRequest.md)|  | 

### Return type

[**SwapOrderResponse**](SwapOrderResponse.md)

### Authorization

[BearerAuth](../README.md#BearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

