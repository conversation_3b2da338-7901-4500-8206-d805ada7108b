# openapi.model.SwapOrderResponseData

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**orderId** | **int** | Internal order ID | [optional] 
**orderNo** | **String** | Order number that was swapped | [optional] 
**orderDate** | [**DateTime**](DateTime.md) | Date of the swapped order | [optional] 
**swapDetails** | [**SwapOrderResponseDataSwapDetails**](SwapOrderResponseDataSwapDetails.md) |  | [optional] 
**reason** | **String** | Reason provided for the swap | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


