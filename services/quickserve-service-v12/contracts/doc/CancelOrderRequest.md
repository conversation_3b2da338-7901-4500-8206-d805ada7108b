# openapi.model.CancelOrderRequest

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**companyId** | **int** | Company id associated with this request | 
**reason** | **String** | Reason for order cancellation | 
**cancelDates** | [**List<DateTime>**](DateTime.md) | Specific dates to cancel | [default to const []]
**mealType** | **String** | Optional: Filter cancellation by specific meal type | [optional] 
**cancelledBy** | **String** | Who initiated the cancellation | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


