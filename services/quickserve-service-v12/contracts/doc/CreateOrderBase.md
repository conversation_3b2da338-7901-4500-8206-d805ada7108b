# openapi.model.CreateOrderBase

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**customerId** | **int** |  | [optional] 
**userId** | **int** |  | [optional] 
**companyId** | **int** |  | [optional] 
**unitId** | **int** |  | [optional] 
**fkKitchenCode** | **int** |  | [optional] 
**customerName** | **String** |  | [optional] 
**customerEmail** | **String** |  | [optional] 
**customerPhone** | **String** |  | [optional] 
**customerAddress** | **String** |  | [optional] 
**locationCode** | **int** |  | [optional] 
**locationName** | **String** |  | [optional] 
**city** | **int** |  | [optional] 
**cityName** | **String** |  | [optional] 
**foodPreference** | **String** |  | [optional] 
**paymentMethod** | **String** | Payment method: - online: Full payment via gateway - wallet: Use wallet balance (automatic partial payment if insufficient)  | [optional] [default to 'online']

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


