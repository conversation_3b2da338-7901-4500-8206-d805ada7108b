# openapi.model.SwapOrderResponseDataSwapDetails

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**oldProduct** | [**SwapOrderResponseDataSwapDetailsOldProduct**](SwapOrderResponseDataSwapDetailsOldProduct.md) |  | [optional] 
**newProduct** | [**SwapOrderResponseDataSwapDetailsNewProduct**](SwapOrderResponseDataSwapDetailsNewProduct.md) |  | [optional] 
**priceDifference** | **num** | Price difference between old and new product | [optional] 
**swapCharges** | **num** | Additional charges for the swap | [optional] 
**totalAmountChange** | **num** | Total amount change (price difference + swap charges) | [optional] 
**newOrderAmount** | **num** | New total order amount after swap | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


