# openapi.model.CustomerOrdersResponseV2Data

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**customer** | [**CustomerOrdersResponseV2DataCustomer**](CustomerOrdersResponseV2DataCustomer.md) |  | [optional] 
**summary** | [**CustomerOrdersResponseV2DataSummary**](CustomerOrdersResponseV2DataSummary.md) |  | [optional] 
**studentNames** | **List<String>** |  | [optional] [default to const []]
**filters** | [**CustomerOrdersResponseV2DataFilters**](CustomerOrdersResponseV2DataFilters.md) |  | [optional] 
**orders** | [**CustomerOrdersResponseV2DataOrders**](CustomerOrdersResponseV2DataOrders.md) |  | [optional] 
**pagination** | [**CustomerOrdersResponseV2DataPagination**](CustomerOrdersResponseV2DataPagination.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


