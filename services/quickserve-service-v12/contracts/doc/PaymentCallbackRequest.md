# openapi.model.PaymentCallbackRequest

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**companyId** | **int** | Company id associated with this callback | 
**transactionId** | **String** | Payment gateway transaction id | 
**gatewayResponse** | [**Object**](.md) | Optional gateway response payload | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


