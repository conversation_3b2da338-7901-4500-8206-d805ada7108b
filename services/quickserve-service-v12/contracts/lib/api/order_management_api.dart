//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class OrderManagementApi {
  OrderManagementApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Apply coupon to pre-order or order
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [ApplyCouponRequest] applyCouponRequest (required):
  Future<Response> orderManagementApplyCouponPostWithHttpInfo(ApplyCouponRequest applyCouponRequest,) async {
    // ignore: prefer_const_declarations
    final path = r'/order-management/apply-coupon';

    // ignore: prefer_final_locals
    Object? postBody = applyCouponRequest;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Apply coupon to pre-order or order
  ///
  /// Parameters:
  ///
  /// * [ApplyCouponRequest] applyCouponRequest (required):
  Future<OrderManagementApplyCouponPost200Response?> orderManagementApplyCouponPost(ApplyCouponRequest applyCouponRequest,) async {
    final response = await orderManagementApplyCouponPostWithHttpInfo(applyCouponRequest,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'OrderManagementApplyCouponPost200Response',) as OrderManagementApplyCouponPost200Response;
    
    }
    return null;
  }

  /// Cancel order with time-based refund processing
  ///
  /// Advanced order cancellation with time-based refund policies and wallet management. Includes cancellation tracking with user details and timestamps. 
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] orderNo (required):
  ///   Order number to cancel
  ///
  /// * [CancelOrderRequest] cancelOrderRequest (required):
  Future<Response> orderManagementCancelOrderNoPostWithHttpInfo(String orderNo, CancelOrderRequest cancelOrderRequest,) async {
    // ignore: prefer_const_declarations
    final path = r'/order-management/cancel/{orderNo}'
      .replaceAll('{orderNo}', orderNo);

    // ignore: prefer_final_locals
    Object? postBody = cancelOrderRequest;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Cancel order with time-based refund processing
  ///
  /// Advanced order cancellation with time-based refund policies and wallet management. Includes cancellation tracking with user details and timestamps. 
  ///
  /// Parameters:
  ///
  /// * [String] orderNo (required):
  ///   Order number to cancel
  ///
  /// * [CancelOrderRequest] cancelOrderRequest (required):
  Future<CancelOrderResponse?> orderManagementCancelOrderNoPost(String orderNo, CancelOrderRequest cancelOrderRequest,) async {
    final response = await orderManagementCancelOrderNoPostWithHttpInfo(orderNo, cancelOrderRequest,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'CancelOrderResponse',) as CancelOrderResponse;
    
    }
    return null;
  }

  /// Create new order with payment integration (Normal and Express)
  ///
  /// Creates a new order with temporary tables and payment integration. Returns payment_service_transaction_id for mobile app payment processing.  Normal Orders: - Supports two models:   1) Repeating model using `start_date`, `selected_days`, `subscription_days`, and a single `meals` cart that repeats.   2) Per-date heterogeneous model using `meals_by_date` to schedule different meals per specific date.  Express Orders: - Set `is_express=true`. System evaluates meal-specific cutoff. - If DB cutoff is `00:00:00` (midnight), same-day express allowed until `08:00:00` (configurable) with extra delivery charge. - Extra delivery charge is per kitchen and meal via setting `K{KITCHEN_ID}_{MEALTYPE}_EXPRESS_EXTRA_DELIVERY_CHARGE`. 
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [OrderManagementCreatePostRequest] orderManagementCreatePostRequest (required):
  Future<Response> orderManagementCreatePostWithHttpInfo(OrderManagementCreatePostRequest orderManagementCreatePostRequest,) async {
    // ignore: prefer_const_declarations
    final path = r'/order-management/create';

    // ignore: prefer_final_locals
    Object? postBody = orderManagementCreatePostRequest;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Create new order with payment integration (Normal and Express)
  ///
  /// Creates a new order with temporary tables and payment integration. Returns payment_service_transaction_id for mobile app payment processing.  Normal Orders: - Supports two models:   1) Repeating model using `start_date`, `selected_days`, `subscription_days`, and a single `meals` cart that repeats.   2) Per-date heterogeneous model using `meals_by_date` to schedule different meals per specific date.  Express Orders: - Set `is_express=true`. System evaluates meal-specific cutoff. - If DB cutoff is `00:00:00` (midnight), same-day express allowed until `08:00:00` (configurable) with extra delivery charge. - Extra delivery charge is per kitchen and meal via setting `K{KITCHEN_ID}_{MEALTYPE}_EXPRESS_EXTRA_DELIVERY_CHARGE`. 
  ///
  /// Parameters:
  ///
  /// * [OrderManagementCreatePostRequest] orderManagementCreatePostRequest (required):
  Future<CreateOrderResponse?> orderManagementCreatePost(OrderManagementCreatePostRequest orderManagementCreatePostRequest,) async {
    final response = await orderManagementCreatePostWithHttpInfo(orderManagementCreatePostRequest,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'CreateOrderResponse',) as CreateOrderResponse;
    
    }
    return null;
  }

  /// Get customer orders with cancellation details
  ///
  /// Retrieves all orders for a specific customer including cancellation details such as cancelled by whom and cancellation date. 
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [int] customerCode (required):
  ///   Customer code
  ///
  /// * [int] companyId (required):
  ///   Company id (required) - must be provided as query parameter for GET requests
  ///
  /// * [bool] includeCancelled:
  ///   Include cancelled orders in response (accepts true/false/1/0/on/off)
  ///
  /// * [String] studentNameFilter:
  ///   Filter orders where ship_address starts with this value (student name prefix)
  ///
  /// * [String] orderStatus:
  ///   Filter by order status
  ///
  /// * [DateTime] startDate:
  ///   Inclusive start date (YYYY-MM-DD) for filtering by order_date
  ///
  /// * [DateTime] endDate:
  ///   Inclusive end date (YYYY-MM-DD) for filtering by order_date
  ///
  /// * [int] perPage:
  ///   Page size for the combined orders list (\"all\")
  ///
  /// * [int] page:
  ///   Page number for the combined orders list (\"all\")
  Future<Response> orderManagementCustomerCustomerCodeGetWithHttpInfo(int customerCode, int companyId, { bool? includeCancelled, String? studentNameFilter, String? orderStatus, DateTime? startDate, DateTime? endDate, int? perPage, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/order-management/customer/{customerCode}'
      .replaceAll('{customerCode}', customerCode.toString());

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'company_id', companyId));
    if (includeCancelled != null) {
      queryParams.addAll(_queryParams('', 'include_cancelled', includeCancelled));
    }
    if (studentNameFilter != null) {
      queryParams.addAll(_queryParams('', 'student_name_filter', studentNameFilter));
    }
    if (orderStatus != null) {
      queryParams.addAll(_queryParams('', 'order_status', orderStatus));
    }
    if (startDate != null) {
      queryParams.addAll(_queryParams('', 'start_date', startDate));
    }
    if (endDate != null) {
      queryParams.addAll(_queryParams('', 'end_date', endDate));
    }
    if (perPage != null) {
      queryParams.addAll(_queryParams('', 'per_page', perPage));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get customer orders with cancellation details
  ///
  /// Retrieves all orders for a specific customer including cancellation details such as cancelled by whom and cancellation date. 
  ///
  /// Parameters:
  ///
  /// * [int] customerCode (required):
  ///   Customer code
  ///
  /// * [int] companyId (required):
  ///   Company id (required) - must be provided as query parameter for GET requests
  ///
  /// * [bool] includeCancelled:
  ///   Include cancelled orders in response (accepts true/false/1/0/on/off)
  ///
  /// * [String] studentNameFilter:
  ///   Filter orders where ship_address starts with this value (student name prefix)
  ///
  /// * [String] orderStatus:
  ///   Filter by order status
  ///
  /// * [DateTime] startDate:
  ///   Inclusive start date (YYYY-MM-DD) for filtering by order_date
  ///
  /// * [DateTime] endDate:
  ///   Inclusive end date (YYYY-MM-DD) for filtering by order_date
  ///
  /// * [int] perPage:
  ///   Page size for the combined orders list (\"all\")
  ///
  /// * [int] page:
  ///   Page number for the combined orders list (\"all\")
  Future<CustomerOrdersResponseV2?> orderManagementCustomerCustomerCodeGet(int customerCode, int companyId, { bool? includeCancelled, String? studentNameFilter, String? orderStatus, DateTime? startDate, DateTime? endDate, int? perPage, int? page, }) async {
    final response = await orderManagementCustomerCustomerCodeGetWithHttpInfo(customerCode, companyId,  includeCancelled: includeCancelled, studentNameFilter: studentNameFilter, orderStatus: orderStatus, startDate: startDate, endDate: endDate, perPage: perPage, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'CustomerOrdersResponseV2',) as CustomerOrdersResponseV2;
    
    }
    return null;
  }

  /// Get order details with payment status
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] orderNo (required):
  ///   Order number to retrieve details for
  ///
  /// * [int] companyId (required):
  ///   Company id (required) - must be provided as query parameter for GET requests
  Future<Response> orderManagementDetailsOrderNoGetWithHttpInfo(String orderNo, int companyId,) async {
    // ignore: prefer_const_declarations
    final path = r'/order-management/details/{orderNo}'
      .replaceAll('{orderNo}', orderNo);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'company_id', companyId));

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get order details with payment status
  ///
  /// Parameters:
  ///
  /// * [String] orderNo (required):
  ///   Order number to retrieve details for
  ///
  /// * [int] companyId (required):
  ///   Company id (required) - must be provided as query parameter for GET requests
  Future<CreateOrderResponse?> orderManagementDetailsOrderNoGet(String orderNo, int companyId,) async {
    final response = await orderManagementDetailsOrderNoGetWithHttpInfo(orderNo, companyId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'CreateOrderResponse',) as CreateOrderResponse;
    
    }
    return null;
  }

  /// Payment failure callback from payment service
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] orderNo (required):
  ///   Order number for payment callback
  ///
  /// * [PaymentCallbackRequest] paymentCallbackRequest (required):
  Future<Response> orderManagementPaymentFailureOrderNoPostWithHttpInfo(String orderNo, PaymentCallbackRequest paymentCallbackRequest,) async {
    // ignore: prefer_const_declarations
    final path = r'/order-management/payment-failure/{orderNo}'
      .replaceAll('{orderNo}', orderNo);

    // ignore: prefer_final_locals
    Object? postBody = paymentCallbackRequest;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Payment failure callback from payment service
  ///
  /// Parameters:
  ///
  /// * [String] orderNo (required):
  ///   Order number for payment callback
  ///
  /// * [PaymentCallbackRequest] paymentCallbackRequest (required):
  Future<ErrorResponse?> orderManagementPaymentFailureOrderNoPost(String orderNo, PaymentCallbackRequest paymentCallbackRequest,) async {
    final response = await orderManagementPaymentFailureOrderNoPostWithHttpInfo(orderNo, paymentCallbackRequest,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'ErrorResponse',) as ErrorResponse;
    
    }
    return null;
  }

  /// Payment success callback from payment service
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] orderNo (required):
  ///   Order number for payment callback
  ///
  /// * [PaymentCallbackRequest] paymentCallbackRequest (required):
  Future<Response> orderManagementPaymentSuccessOrderNoPostWithHttpInfo(String orderNo, PaymentCallbackRequest paymentCallbackRequest,) async {
    // ignore: prefer_const_declarations
    final path = r'/order-management/payment-success/{orderNo}'
      .replaceAll('{orderNo}', orderNo);

    // ignore: prefer_final_locals
    Object? postBody = paymentCallbackRequest;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Payment success callback from payment service
  ///
  /// Parameters:
  ///
  /// * [String] orderNo (required):
  ///   Order number for payment callback
  ///
  /// * [PaymentCallbackRequest] paymentCallbackRequest (required):
  Future<ErrorResponse?> orderManagementPaymentSuccessOrderNoPost(String orderNo, PaymentCallbackRequest paymentCallbackRequest,) async {
    final response = await orderManagementPaymentSuccessOrderNoPostWithHttpInfo(orderNo, paymentCallbackRequest,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'ErrorResponse',) as ErrorResponse;
    
    }
    return null;
  }

  /// Check pre-order status for a given order number
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] orderNo (required):
  ///   Pre-order number to check status
  ///
  /// * [int] companyId (required):
  ///   Company id (required) - must be provided as query parameter for GET requests
  Future<Response> orderManagementPreOrderStatusOrderNoGetWithHttpInfo(String orderNo, int companyId,) async {
    // ignore: prefer_const_declarations
    final path = r'/order-management/pre-order-status/{orderNo}'
      .replaceAll('{orderNo}', orderNo);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'company_id', companyId));

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Check pre-order status for a given order number
  ///
  /// Parameters:
  ///
  /// * [String] orderNo (required):
  ///   Pre-order number to check status
  ///
  /// * [int] companyId (required):
  ///   Company id (required) - must be provided as query parameter for GET requests
  Future<OrderManagementPreOrderStatusOrderNoGet200Response?> orderManagementPreOrderStatusOrderNoGet(String orderNo, int companyId,) async {
    final response = await orderManagementPreOrderStatusOrderNoGetWithHttpInfo(orderNo, companyId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'OrderManagementPreOrderStatusOrderNoGet200Response',) as OrderManagementPreOrderStatusOrderNoGet200Response;
    
    }
    return null;
  }

  /// Swap order product with another product from the same category
  ///
  /// Allows customers to swap their meal with another product from the same category. Updates both orders and order_details tables while maintaining data integrity.  **Key Features:** - Product category validation - Only same category swaps allowed - Order status validation - Only swappable orders (not delivered/cancelled) - Price difference calculation - Automatic price adjustment - Swap charges support - Additional charges for premium swaps - Tax recalculation - Updated tax based on new amount - Audit logging - Complete swap history tracking  **Price Calculation:** ``` New Amount = Old Amount + Price Difference + Swap Charges ``` 
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] orderNo (required):
  ///   Order number to swap
  ///
  /// * [SwapOrderRequest] swapOrderRequest (required):
  Future<Response> orderManagementSwapOrderNoPostWithHttpInfo(String orderNo, SwapOrderRequest swapOrderRequest,) async {
    // ignore: prefer_const_declarations
    final path = r'/order-management/swap/{orderNo}'
      .replaceAll('{orderNo}', orderNo);

    // ignore: prefer_final_locals
    Object? postBody = swapOrderRequest;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Swap order product with another product from the same category
  ///
  /// Allows customers to swap their meal with another product from the same category. Updates both orders and order_details tables while maintaining data integrity.  **Key Features:** - Product category validation - Only same category swaps allowed - Order status validation - Only swappable orders (not delivered/cancelled) - Price difference calculation - Automatic price adjustment - Swap charges support - Additional charges for premium swaps - Tax recalculation - Updated tax based on new amount - Audit logging - Complete swap history tracking  **Price Calculation:** ``` New Amount = Old Amount + Price Difference + Swap Charges ``` 
  ///
  /// Parameters:
  ///
  /// * [String] orderNo (required):
  ///   Order number to swap
  ///
  /// * [SwapOrderRequest] swapOrderRequest (required):
  Future<SwapOrderResponse?> orderManagementSwapOrderNoPost(String orderNo, SwapOrderRequest swapOrderRequest,) async {
    final response = await orderManagementSwapOrderNoPostWithHttpInfo(orderNo, swapOrderRequest,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SwapOrderResponse',) as SwapOrderResponse;
    
    }
    return null;
  }
}
