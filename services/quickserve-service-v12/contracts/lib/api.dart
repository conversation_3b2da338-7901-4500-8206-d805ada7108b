//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

library openapi.api;

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:collection/collection.dart';
import 'package:http/http.dart';
import 'package:intl/intl.dart';
import 'package:meta/meta.dart';

part 'api_client.dart';
part 'api_helper.dart';
part 'api_exception.dart';
part 'auth/authentication.dart';
part 'auth/api_key_auth.dart';
part 'auth/oauth.dart';
part 'auth/http_basic_auth.dart';
part 'auth/http_bearer_auth.dart';

part 'api/order_management_api.dart';

part 'model/apply_coupon_request.dart';
part 'model/cancel_order_request.dart';
part 'model/cancel_order_response.dart';
part 'model/cancel_order_response_data.dart';
part 'model/cancel_order_response_data_cancellation_details.dart';
part 'model/create_order_base.dart';
part 'model/create_order_request.dart';
part 'model/create_order_response.dart';
part 'model/create_order_response_data.dart';
part 'model/create_order_response_data_express.dart';
part 'model/customer_orders_response.dart';
part 'model/customer_orders_response_v2.dart';
part 'model/customer_orders_response_v2_data.dart';
part 'model/customer_orders_response_v2_data_customer.dart';
part 'model/customer_orders_response_v2_data_filters.dart';
part 'model/customer_orders_response_v2_data_orders.dart';
part 'model/customer_orders_response_v2_data_pagination.dart';
part 'model/customer_orders_response_v2_data_summary.dart';
part 'model/error_response.dart';
part 'model/express_create_order_request.dart';
part 'model/meal_cart_item.dart';
part 'model/meal_cart_item_short.dart';
part 'model/meals_by_date_entry.dart';
part 'model/normal_create_order_request.dart';
part 'model/normal_per_date_model.dart';
part 'model/normal_repeating_model.dart';
part 'model/order_list_item.dart';
part 'model/order_management_apply_coupon_post200_response.dart';
part 'model/order_management_create_post_request.dart';
part 'model/order_management_pre_order_status_order_no_get200_response.dart';
part 'model/pagination_meta.dart';
part 'model/payment_callback_request.dart';
part 'model/per_date_meal_item.dart';
part 'model/swap_order_request.dart';
part 'model/swap_order_response.dart';
part 'model/swap_order_response_data.dart';
part 'model/swap_order_response_data_swap_details.dart';
part 'model/swap_order_response_data_swap_details_new_product.dart';
part 'model/swap_order_response_data_swap_details_old_product.dart';
part 'model/validation_error_response.dart';


/// An [ApiClient] instance that uses the default values obtained from
/// the OpenAPI specification file.
var defaultApiClient = ApiClient();

const _delimiters = {'csv': ',', 'ssv': ' ', 'tsv': '\t', 'pipes': '|'};
const _dateEpochMarker = 'epoch';
const _deepEquality = DeepCollectionEquality();
final _dateFormatter = DateFormat('yyyy-MM-dd');
final _regList = RegExp(r'^List<(.*)>$');
final _regSet = RegExp(r'^Set<(.*)>$');
final _regMap = RegExp(r'^Map<String,(.*)>$');

bool _isEpochMarker(String? pattern) => pattern == _dateEpochMarker || pattern == '/$_dateEpochMarker/';
