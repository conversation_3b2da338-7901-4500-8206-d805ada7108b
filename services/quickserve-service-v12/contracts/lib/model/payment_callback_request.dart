//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class PaymentCallbackRequest {
  /// Returns a new [PaymentCallbackRequest] instance.
  PaymentCallbackRequest({
    required this.companyId,
    required this.transactionId,
    this.gatewayResponse,
  });

  /// Company id associated with this callback
  int companyId;

  /// Payment gateway transaction id
  String transactionId;

  /// Optional gateway response payload
  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  Object? gatewayResponse;

  @override
  bool operator ==(Object other) => identical(this, other) || other is PaymentCallbackRequest &&
    other.companyId == companyId &&
    other.transactionId == transactionId &&
    other.gatewayResponse == gatewayResponse;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (companyId.hashCode) +
    (transactionId.hashCode) +
    (gatewayResponse == null ? 0 : gatewayResponse!.hashCode);

  @override
  String toString() => 'PaymentCallbackRequest[companyId=$companyId, transactionId=$transactionId, gatewayResponse=$gatewayResponse]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'company_id'] = this.companyId;
      json[r'transaction_id'] = this.transactionId;
    if (this.gatewayResponse != null) {
      json[r'gateway_response'] = this.gatewayResponse;
    } else {
      json[r'gateway_response'] = null;
    }
    return json;
  }

  /// Returns a new [PaymentCallbackRequest] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static PaymentCallbackRequest? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "PaymentCallbackRequest[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "PaymentCallbackRequest[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return PaymentCallbackRequest(
        companyId: mapValueOfType<int>(json, r'company_id')!,
        transactionId: mapValueOfType<String>(json, r'transaction_id')!,
        gatewayResponse: mapValueOfType<Object>(json, r'gateway_response'),
      );
    }
    return null;
  }

  static List<PaymentCallbackRequest> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <PaymentCallbackRequest>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = PaymentCallbackRequest.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, PaymentCallbackRequest> mapFromJson(dynamic json) {
    final map = <String, PaymentCallbackRequest>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = PaymentCallbackRequest.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of PaymentCallbackRequest-objects as value to a dart map
  static Map<String, List<PaymentCallbackRequest>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<PaymentCallbackRequest>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = PaymentCallbackRequest.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'company_id',
    'transaction_id',
  };
}

