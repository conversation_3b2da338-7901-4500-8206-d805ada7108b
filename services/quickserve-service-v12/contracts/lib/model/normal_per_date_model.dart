//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class NormalPerDateModel {
  /// Returns a new [NormalPerDateModel] instance.
  NormalPerDateModel({
    this.mealsByDate = const [],
  });

  List<MealsByDateEntry> mealsByDate;

  @override
  bool operator ==(Object other) => identical(this, other) || other is NormalPerDateModel &&
    _deepEquality.equals(other.mealsByDate, mealsByDate);

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (mealsByDate.hashCode);

  @override
  String toString() => 'NormalPerDateModel[mealsByDate=$mealsByDate]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'meals_by_date'] = this.mealsByDate;
    return json;
  }

  /// Returns a new [NormalPerDateModel] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static NormalPerDateModel? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "NormalPerDateModel[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "NormalPerDateModel[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return NormalPerDateModel(
        mealsByDate: MealsByDateEntry.listFromJson(json[r'meals_by_date']),
      );
    }
    return null;
  }

  static List<NormalPerDateModel> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <NormalPerDateModel>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = NormalPerDateModel.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, NormalPerDateModel> mapFromJson(dynamic json) {
    final map = <String, NormalPerDateModel>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = NormalPerDateModel.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of NormalPerDateModel-objects as value to a dart map
  static Map<String, List<NormalPerDateModel>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<NormalPerDateModel>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = NormalPerDateModel.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'meals_by_date',
  };
}

