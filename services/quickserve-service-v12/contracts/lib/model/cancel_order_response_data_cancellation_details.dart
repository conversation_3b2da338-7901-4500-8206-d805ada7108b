//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class CancelOrderResponseDataCancellationDetails {
  /// Returns a new [CancelOrderResponseDataCancellationDetails] instance.
  CancelOrderResponseDataCancellationDetails({
    this.cancelledBy,
    this.cancelledOn,
    this.cancellationReason,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? cancelledBy;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DateTime? cancelledOn;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? cancellationReason;

  @override
  bool operator ==(Object other) => identical(this, other) || other is CancelOrderResponseDataCancellationDetails &&
    other.cancelledBy == cancelledBy &&
    other.cancelledOn == cancelledOn &&
    other.cancellationReason == cancellationReason;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (cancelledBy == null ? 0 : cancelledBy!.hashCode) +
    (cancelledOn == null ? 0 : cancelledOn!.hashCode) +
    (cancellationReason == null ? 0 : cancellationReason!.hashCode);

  @override
  String toString() => 'CancelOrderResponseDataCancellationDetails[cancelledBy=$cancelledBy, cancelledOn=$cancelledOn, cancellationReason=$cancellationReason]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.cancelledBy != null) {
      json[r'cancelled_by'] = this.cancelledBy;
    } else {
      json[r'cancelled_by'] = null;
    }
    if (this.cancelledOn != null) {
      json[r'cancelled_on'] = this.cancelledOn!.toUtc().toIso8601String();
    } else {
      json[r'cancelled_on'] = null;
    }
    if (this.cancellationReason != null) {
      json[r'cancellation_reason'] = this.cancellationReason;
    } else {
      json[r'cancellation_reason'] = null;
    }
    return json;
  }

  /// Returns a new [CancelOrderResponseDataCancellationDetails] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static CancelOrderResponseDataCancellationDetails? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "CancelOrderResponseDataCancellationDetails[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "CancelOrderResponseDataCancellationDetails[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return CancelOrderResponseDataCancellationDetails(
        cancelledBy: mapValueOfType<String>(json, r'cancelled_by'),
        cancelledOn: mapDateTime(json, r'cancelled_on', r''),
        cancellationReason: mapValueOfType<String>(json, r'cancellation_reason'),
      );
    }
    return null;
  }

  static List<CancelOrderResponseDataCancellationDetails> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CancelOrderResponseDataCancellationDetails>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CancelOrderResponseDataCancellationDetails.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, CancelOrderResponseDataCancellationDetails> mapFromJson(dynamic json) {
    final map = <String, CancelOrderResponseDataCancellationDetails>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = CancelOrderResponseDataCancellationDetails.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of CancelOrderResponseDataCancellationDetails-objects as value to a dart map
  static Map<String, List<CancelOrderResponseDataCancellationDetails>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<CancelOrderResponseDataCancellationDetails>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = CancelOrderResponseDataCancellationDetails.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

