//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class ExpressCreateOrderRequest {
  /// Returns a new [ExpressCreateOrderRequest] instance.
  ExpressCreateOrderRequest({
    required this.customerId,
    required this.userId,
    required this.companyId,
    required this.unitId,
    required this.fkKitchenCode,
    this.customerName,
    this.customerEmail,
    this.customerPhone,
    required this.customerAddress,
    required this.locationCode,
    required this.locationName,
    required this.city,
    required this.cityName,
    this.foodPreference,
    this.paymentMethod = const ExpressCreateOrderRequestPaymentMethodEnum._('online'),
    required this.isExpress,
    this.meals = const [],
    required this.startDate,
    this.selectedDays = const [],
    required this.subscriptionDays,
    this.mealsByDate = const [],
  });

  int customerId;

  int userId;

  int companyId;

  int unitId;

  int fkKitchenCode;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? customerName;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? customerEmail;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? customerPhone;

  String customerAddress;

  int locationCode;

  String locationName;

  int city;

  String cityName;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? foodPreference;

  /// Payment method: - online: Full payment via gateway - wallet: Use wallet balance (automatic partial payment if insufficient) 
  ExpressCreateOrderRequestPaymentMethodEnum paymentMethod;

  bool isExpress;

  List<MealCartItemShort> meals;

  DateTime startDate;

  List<int> selectedDays;

  /// Minimum value: 1
  /// Maximum value: 300
  int subscriptionDays;

  List<MealsByDateEntry> mealsByDate;

  @override
  bool operator ==(Object other) => identical(this, other) || other is ExpressCreateOrderRequest &&
    other.customerId == customerId &&
    other.userId == userId &&
    other.companyId == companyId &&
    other.unitId == unitId &&
    other.fkKitchenCode == fkKitchenCode &&
    other.customerName == customerName &&
    other.customerEmail == customerEmail &&
    other.customerPhone == customerPhone &&
    other.customerAddress == customerAddress &&
    other.locationCode == locationCode &&
    other.locationName == locationName &&
    other.city == city &&
    other.cityName == cityName &&
    other.foodPreference == foodPreference &&
    other.paymentMethod == paymentMethod &&
    other.isExpress == isExpress &&
    _deepEquality.equals(other.meals, meals) &&
    other.startDate == startDate &&
    _deepEquality.equals(other.selectedDays, selectedDays) &&
    other.subscriptionDays == subscriptionDays &&
    _deepEquality.equals(other.mealsByDate, mealsByDate);

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (customerId.hashCode) +
    (userId.hashCode) +
    (companyId.hashCode) +
    (unitId.hashCode) +
    (fkKitchenCode.hashCode) +
    (customerName == null ? 0 : customerName!.hashCode) +
    (customerEmail == null ? 0 : customerEmail!.hashCode) +
    (customerPhone == null ? 0 : customerPhone!.hashCode) +
    (customerAddress.hashCode) +
    (locationCode.hashCode) +
    (locationName.hashCode) +
    (city.hashCode) +
    (cityName.hashCode) +
    (foodPreference == null ? 0 : foodPreference!.hashCode) +
    (paymentMethod.hashCode) +
    (isExpress.hashCode) +
    (meals.hashCode) +
    (startDate.hashCode) +
    (selectedDays.hashCode) +
    (subscriptionDays.hashCode) +
    (mealsByDate.hashCode);

  @override
  String toString() => 'ExpressCreateOrderRequest[customerId=$customerId, userId=$userId, companyId=$companyId, unitId=$unitId, fkKitchenCode=$fkKitchenCode, customerName=$customerName, customerEmail=$customerEmail, customerPhone=$customerPhone, customerAddress=$customerAddress, locationCode=$locationCode, locationName=$locationName, city=$city, cityName=$cityName, foodPreference=$foodPreference, paymentMethod=$paymentMethod, isExpress=$isExpress, meals=$meals, startDate=$startDate, selectedDays=$selectedDays, subscriptionDays=$subscriptionDays, mealsByDate=$mealsByDate]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'customer_id'] = this.customerId;
      json[r'user_id'] = this.userId;
      json[r'company_id'] = this.companyId;
      json[r'unit_id'] = this.unitId;
      json[r'fk_kitchen_code'] = this.fkKitchenCode;
    if (this.customerName != null) {
      json[r'customer_name'] = this.customerName;
    } else {
      json[r'customer_name'] = null;
    }
    if (this.customerEmail != null) {
      json[r'customer_email'] = this.customerEmail;
    } else {
      json[r'customer_email'] = null;
    }
    if (this.customerPhone != null) {
      json[r'customer_phone'] = this.customerPhone;
    } else {
      json[r'customer_phone'] = null;
    }
      json[r'customer_address'] = this.customerAddress;
      json[r'location_code'] = this.locationCode;
      json[r'location_name'] = this.locationName;
      json[r'city'] = this.city;
      json[r'city_name'] = this.cityName;
    if (this.foodPreference != null) {
      json[r'food_preference'] = this.foodPreference;
    } else {
      json[r'food_preference'] = null;
    }
      json[r'payment_method'] = this.paymentMethod;
      json[r'is_express'] = this.isExpress;
      json[r'meals'] = this.meals;
      json[r'start_date'] = _dateFormatter.format(this.startDate.toUtc());
      json[r'selected_days'] = this.selectedDays;
      json[r'subscription_days'] = this.subscriptionDays;
      json[r'meals_by_date'] = this.mealsByDate;
    return json;
  }

  /// Returns a new [ExpressCreateOrderRequest] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static ExpressCreateOrderRequest? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "ExpressCreateOrderRequest[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "ExpressCreateOrderRequest[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return ExpressCreateOrderRequest(
        customerId: mapValueOfType<int>(json, r'customer_id')!,
        userId: mapValueOfType<int>(json, r'user_id')!,
        companyId: mapValueOfType<int>(json, r'company_id')!,
        unitId: mapValueOfType<int>(json, r'unit_id')!,
        fkKitchenCode: mapValueOfType<int>(json, r'fk_kitchen_code')!,
        customerName: mapValueOfType<String>(json, r'customer_name'),
        customerEmail: mapValueOfType<String>(json, r'customer_email'),
        customerPhone: mapValueOfType<String>(json, r'customer_phone'),
        customerAddress: mapValueOfType<String>(json, r'customer_address')!,
        locationCode: mapValueOfType<int>(json, r'location_code')!,
        locationName: mapValueOfType<String>(json, r'location_name')!,
        city: mapValueOfType<int>(json, r'city')!,
        cityName: mapValueOfType<String>(json, r'city_name')!,
        foodPreference: mapValueOfType<String>(json, r'food_preference'),
        paymentMethod: ExpressCreateOrderRequestPaymentMethodEnum.fromJson(json[r'payment_method']) ?? 'online',
        isExpress: mapValueOfType<bool>(json, r'is_express')!,
        meals: MealCartItemShort.listFromJson(json[r'meals']),
        startDate: mapDateTime(json, r'start_date', r'')!,
        selectedDays: json[r'selected_days'] is Iterable
            ? (json[r'selected_days'] as Iterable).cast<int>().toList(growable: false)
            : const [],
        subscriptionDays: mapValueOfType<int>(json, r'subscription_days')!,
        mealsByDate: MealsByDateEntry.listFromJson(json[r'meals_by_date']),
      );
    }
    return null;
  }

  static List<ExpressCreateOrderRequest> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ExpressCreateOrderRequest>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ExpressCreateOrderRequest.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, ExpressCreateOrderRequest> mapFromJson(dynamic json) {
    final map = <String, ExpressCreateOrderRequest>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ExpressCreateOrderRequest.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of ExpressCreateOrderRequest-objects as value to a dart map
  static Map<String, List<ExpressCreateOrderRequest>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<ExpressCreateOrderRequest>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = ExpressCreateOrderRequest.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'customer_id',
    'user_id',
    'company_id',
    'unit_id',
    'fk_kitchen_code',
    'customer_address',
    'location_code',
    'location_name',
    'city',
    'city_name',
    'is_express',
    'meals',
    'start_date',
    'selected_days',
    'subscription_days',
    'meals_by_date',
  };
}

/// Payment method: - online: Full payment via gateway - wallet: Use wallet balance (automatic partial payment if insufficient) 
class ExpressCreateOrderRequestPaymentMethodEnum {
  /// Instantiate a new enum with the provided [value].
  const ExpressCreateOrderRequestPaymentMethodEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const online = ExpressCreateOrderRequestPaymentMethodEnum._(r'online');
  static const wallet = ExpressCreateOrderRequestPaymentMethodEnum._(r'wallet');

  /// List of all possible values in this [enum][ExpressCreateOrderRequestPaymentMethodEnum].
  static const values = <ExpressCreateOrderRequestPaymentMethodEnum>[
    online,
    wallet,
  ];

  static ExpressCreateOrderRequestPaymentMethodEnum? fromJson(dynamic value) => ExpressCreateOrderRequestPaymentMethodEnumTypeTransformer().decode(value);

  static List<ExpressCreateOrderRequestPaymentMethodEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ExpressCreateOrderRequestPaymentMethodEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ExpressCreateOrderRequestPaymentMethodEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [ExpressCreateOrderRequestPaymentMethodEnum] to String,
/// and [decode] dynamic data back to [ExpressCreateOrderRequestPaymentMethodEnum].
class ExpressCreateOrderRequestPaymentMethodEnumTypeTransformer {
  factory ExpressCreateOrderRequestPaymentMethodEnumTypeTransformer() => _instance ??= const ExpressCreateOrderRequestPaymentMethodEnumTypeTransformer._();

  const ExpressCreateOrderRequestPaymentMethodEnumTypeTransformer._();

  String encode(ExpressCreateOrderRequestPaymentMethodEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a ExpressCreateOrderRequestPaymentMethodEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  ExpressCreateOrderRequestPaymentMethodEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'online': return ExpressCreateOrderRequestPaymentMethodEnum.online;
        case r'wallet': return ExpressCreateOrderRequestPaymentMethodEnum.wallet;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [ExpressCreateOrderRequestPaymentMethodEnumTypeTransformer] instance.
  static ExpressCreateOrderRequestPaymentMethodEnumTypeTransformer? _instance;
}


