//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class MealCartItem {
  /// Returns a new [MealCartItem] instance.
  MealCartItem({
    required this.productCode,
    required this.productName,
    required this.quantity,
    required this.amount,
  });

  /// Product code (details fetched from products table)
  int productCode;

  /// Name of the product
  String productName;

  /// Quantity of this meal
  ///
  /// Minimum value: 1
  int quantity;

  /// Price for this quantity of the product
  double amount;

  @override
  bool operator ==(Object other) => identical(this, other) || other is MealCartItem &&
    other.productCode == productCode &&
    other.productName == productName &&
    other.quantity == quantity &&
    other.amount == amount;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (productCode.hashCode) +
    (productName.hashCode) +
    (quantity.hashCode) +
    (amount.hashCode);

  @override
  String toString() => 'MealCartItem[productCode=$productCode, productName=$productName, quantity=$quantity, amount=$amount]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'product_code'] = this.productCode;
      json[r'product_name'] = this.productName;
      json[r'quantity'] = this.quantity;
      json[r'amount'] = this.amount;
    return json;
  }

  /// Returns a new [MealCartItem] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static MealCartItem? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "MealCartItem[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "MealCartItem[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return MealCartItem(
        productCode: mapValueOfType<int>(json, r'product_code')!,
        productName: mapValueOfType<String>(json, r'product_name')!,
        quantity: mapValueOfType<int>(json, r'quantity')!,
        amount: mapValueOfType<double>(json, r'amount')!,
      );
    }
    return null;
  }

  static List<MealCartItem> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <MealCartItem>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = MealCartItem.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, MealCartItem> mapFromJson(dynamic json) {
    final map = <String, MealCartItem>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = MealCartItem.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of MealCartItem-objects as value to a dart map
  static Map<String, List<MealCartItem>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<MealCartItem>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = MealCartItem.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'product_code',
    'product_name',
    'quantity',
    'amount',
  };
}

