//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class OrderManagementPreOrderStatusOrderNoGet200Response {
  /// Returns a new [OrderManagementPreOrderStatusOrderNoGet200Response] instance.
  OrderManagementPreOrderStatusOrderNoGet200Response({
    this.success,
    this.status,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? success;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? status;

  @override
  bool operator ==(Object other) => identical(this, other) || other is OrderManagementPreOrderStatusOrderNoGet200Response &&
    other.success == success &&
    other.status == status;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (success == null ? 0 : success!.hashCode) +
    (status == null ? 0 : status!.hashCode);

  @override
  String toString() => 'OrderManagementPreOrderStatusOrderNoGet200Response[success=$success, status=$status]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.success != null) {
      json[r'success'] = this.success;
    } else {
      json[r'success'] = null;
    }
    if (this.status != null) {
      json[r'status'] = this.status;
    } else {
      json[r'status'] = null;
    }
    return json;
  }

  /// Returns a new [OrderManagementPreOrderStatusOrderNoGet200Response] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static OrderManagementPreOrderStatusOrderNoGet200Response? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "OrderManagementPreOrderStatusOrderNoGet200Response[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "OrderManagementPreOrderStatusOrderNoGet200Response[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return OrderManagementPreOrderStatusOrderNoGet200Response(
        success: mapValueOfType<bool>(json, r'success'),
        status: mapValueOfType<String>(json, r'status'),
      );
    }
    return null;
  }

  static List<OrderManagementPreOrderStatusOrderNoGet200Response> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <OrderManagementPreOrderStatusOrderNoGet200Response>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = OrderManagementPreOrderStatusOrderNoGet200Response.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, OrderManagementPreOrderStatusOrderNoGet200Response> mapFromJson(dynamic json) {
    final map = <String, OrderManagementPreOrderStatusOrderNoGet200Response>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = OrderManagementPreOrderStatusOrderNoGet200Response.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of OrderManagementPreOrderStatusOrderNoGet200Response-objects as value to a dart map
  static Map<String, List<OrderManagementPreOrderStatusOrderNoGet200Response>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<OrderManagementPreOrderStatusOrderNoGet200Response>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = OrderManagementPreOrderStatusOrderNoGet200Response.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

