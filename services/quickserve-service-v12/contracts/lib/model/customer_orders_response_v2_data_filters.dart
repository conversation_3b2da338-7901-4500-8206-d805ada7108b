//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class CustomerOrdersResponseV2DataFilters {
  /// Returns a new [CustomerOrdersResponseV2DataFilters] instance.
  CustomerOrdersResponseV2DataFilters({
    this.studentNameFilter,
    this.includeCancelled,
    this.orderStatus,
    this.startDate,
    this.endDate,
    this.perPage,
    this.pageUpcoming,
    this.pageCancelled,
    this.pageOther,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? studentNameFilter;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? includeCancelled;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? orderStatus;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DateTime? startDate;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DateTime? endDate;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? perPage;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? pageUpcoming;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? pageCancelled;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? pageOther;

  @override
  bool operator ==(Object other) => identical(this, other) || other is CustomerOrdersResponseV2DataFilters &&
    other.studentNameFilter == studentNameFilter &&
    other.includeCancelled == includeCancelled &&
    other.orderStatus == orderStatus &&
    other.startDate == startDate &&
    other.endDate == endDate &&
    other.perPage == perPage &&
    other.pageUpcoming == pageUpcoming &&
    other.pageCancelled == pageCancelled &&
    other.pageOther == pageOther;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (studentNameFilter == null ? 0 : studentNameFilter!.hashCode) +
    (includeCancelled == null ? 0 : includeCancelled!.hashCode) +
    (orderStatus == null ? 0 : orderStatus!.hashCode) +
    (startDate == null ? 0 : startDate!.hashCode) +
    (endDate == null ? 0 : endDate!.hashCode) +
    (perPage == null ? 0 : perPage!.hashCode) +
    (pageUpcoming == null ? 0 : pageUpcoming!.hashCode) +
    (pageCancelled == null ? 0 : pageCancelled!.hashCode) +
    (pageOther == null ? 0 : pageOther!.hashCode);

  @override
  String toString() => 'CustomerOrdersResponseV2DataFilters[studentNameFilter=$studentNameFilter, includeCancelled=$includeCancelled, orderStatus=$orderStatus, startDate=$startDate, endDate=$endDate, perPage=$perPage, pageUpcoming=$pageUpcoming, pageCancelled=$pageCancelled, pageOther=$pageOther]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.studentNameFilter != null) {
      json[r'student_name_filter'] = this.studentNameFilter;
    } else {
      json[r'student_name_filter'] = null;
    }
    if (this.includeCancelled != null) {
      json[r'include_cancelled'] = this.includeCancelled;
    } else {
      json[r'include_cancelled'] = null;
    }
    if (this.orderStatus != null) {
      json[r'order_status'] = this.orderStatus;
    } else {
      json[r'order_status'] = null;
    }
    if (this.startDate != null) {
      json[r'start_date'] = _dateFormatter.format(this.startDate!.toUtc());
    } else {
      json[r'start_date'] = null;
    }
    if (this.endDate != null) {
      json[r'end_date'] = _dateFormatter.format(this.endDate!.toUtc());
    } else {
      json[r'end_date'] = null;
    }
    if (this.perPage != null) {
      json[r'per_page'] = this.perPage;
    } else {
      json[r'per_page'] = null;
    }
    if (this.pageUpcoming != null) {
      json[r'page_upcoming'] = this.pageUpcoming;
    } else {
      json[r'page_upcoming'] = null;
    }
    if (this.pageCancelled != null) {
      json[r'page_cancelled'] = this.pageCancelled;
    } else {
      json[r'page_cancelled'] = null;
    }
    if (this.pageOther != null) {
      json[r'page_other'] = this.pageOther;
    } else {
      json[r'page_other'] = null;
    }
    return json;
  }

  /// Returns a new [CustomerOrdersResponseV2DataFilters] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static CustomerOrdersResponseV2DataFilters? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "CustomerOrdersResponseV2DataFilters[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "CustomerOrdersResponseV2DataFilters[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return CustomerOrdersResponseV2DataFilters(
        studentNameFilter: mapValueOfType<String>(json, r'student_name_filter'),
        includeCancelled: mapValueOfType<bool>(json, r'include_cancelled'),
        orderStatus: mapValueOfType<String>(json, r'order_status'),
        startDate: mapDateTime(json, r'start_date', r''),
        endDate: mapDateTime(json, r'end_date', r''),
        perPage: mapValueOfType<int>(json, r'per_page'),
        pageUpcoming: mapValueOfType<int>(json, r'page_upcoming'),
        pageCancelled: mapValueOfType<int>(json, r'page_cancelled'),
        pageOther: mapValueOfType<int>(json, r'page_other'),
      );
    }
    return null;
  }

  static List<CustomerOrdersResponseV2DataFilters> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CustomerOrdersResponseV2DataFilters>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CustomerOrdersResponseV2DataFilters.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, CustomerOrdersResponseV2DataFilters> mapFromJson(dynamic json) {
    final map = <String, CustomerOrdersResponseV2DataFilters>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = CustomerOrdersResponseV2DataFilters.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of CustomerOrdersResponseV2DataFilters-objects as value to a dart map
  static Map<String, List<CustomerOrdersResponseV2DataFilters>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<CustomerOrdersResponseV2DataFilters>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = CustomerOrdersResponseV2DataFilters.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

