//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SwapOrderRequest {
  /// Returns a new [SwapOrderRequest] instance.
  SwapOrderRequest({
    required this.companyId,
    required this.orderDate,
    required this.newProductCode,
    this.reason,
    this.mealType,
  });

  /// Company id associated with this request
  int companyId;

  /// Date of the order to swap (YYYY-MM-DD)
  DateTime orderDate;

  /// Product code of the new product to swap to
  int newProductCode;

  /// Optional reason for the swap
  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? reason;

  /// Optional filter by meal type
  SwapOrderRequestMealTypeEnum? mealType;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SwapOrderRequest &&
    other.companyId == companyId &&
    other.orderDate == orderDate &&
    other.newProductCode == newProductCode &&
    other.reason == reason &&
    other.mealType == mealType;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (companyId.hashCode) +
    (orderDate.hashCode) +
    (newProductCode.hashCode) +
    (reason == null ? 0 : reason!.hashCode) +
    (mealType == null ? 0 : mealType!.hashCode);

  @override
  String toString() => 'SwapOrderRequest[companyId=$companyId, orderDate=$orderDate, newProductCode=$newProductCode, reason=$reason, mealType=$mealType]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'company_id'] = this.companyId;
      json[r'order_date'] = _dateFormatter.format(this.orderDate.toUtc());
      json[r'new_product_code'] = this.newProductCode;
    if (this.reason != null) {
      json[r'reason'] = this.reason;
    } else {
      json[r'reason'] = null;
    }
    if (this.mealType != null) {
      json[r'meal_type'] = this.mealType;
    } else {
      json[r'meal_type'] = null;
    }
    return json;
  }

  /// Returns a new [SwapOrderRequest] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SwapOrderRequest? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SwapOrderRequest[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SwapOrderRequest[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SwapOrderRequest(
        companyId: mapValueOfType<int>(json, r'company_id')!,
        orderDate: mapDateTime(json, r'order_date', r'')!,
        newProductCode: mapValueOfType<int>(json, r'new_product_code')!,
        reason: mapValueOfType<String>(json, r'reason'),
        mealType: SwapOrderRequestMealTypeEnum.fromJson(json[r'meal_type']),
      );
    }
    return null;
  }

  static List<SwapOrderRequest> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SwapOrderRequest>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SwapOrderRequest.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SwapOrderRequest> mapFromJson(dynamic json) {
    final map = <String, SwapOrderRequest>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SwapOrderRequest.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SwapOrderRequest-objects as value to a dart map
  static Map<String, List<SwapOrderRequest>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SwapOrderRequest>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SwapOrderRequest.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'company_id',
    'order_date',
    'new_product_code',
  };
}

/// Optional filter by meal type
class SwapOrderRequestMealTypeEnum {
  /// Instantiate a new enum with the provided [value].
  const SwapOrderRequestMealTypeEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const breakfast = SwapOrderRequestMealTypeEnum._(r'breakfast');
  static const lunch = SwapOrderRequestMealTypeEnum._(r'lunch');
  static const dinner = SwapOrderRequestMealTypeEnum._(r'dinner');

  /// List of all possible values in this [enum][SwapOrderRequestMealTypeEnum].
  static const values = <SwapOrderRequestMealTypeEnum>[
    breakfast,
    lunch,
    dinner,
  ];

  static SwapOrderRequestMealTypeEnum? fromJson(dynamic value) => SwapOrderRequestMealTypeEnumTypeTransformer().decode(value);

  static List<SwapOrderRequestMealTypeEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SwapOrderRequestMealTypeEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SwapOrderRequestMealTypeEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [SwapOrderRequestMealTypeEnum] to String,
/// and [decode] dynamic data back to [SwapOrderRequestMealTypeEnum].
class SwapOrderRequestMealTypeEnumTypeTransformer {
  factory SwapOrderRequestMealTypeEnumTypeTransformer() => _instance ??= const SwapOrderRequestMealTypeEnumTypeTransformer._();

  const SwapOrderRequestMealTypeEnumTypeTransformer._();

  String encode(SwapOrderRequestMealTypeEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a SwapOrderRequestMealTypeEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  SwapOrderRequestMealTypeEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'breakfast': return SwapOrderRequestMealTypeEnum.breakfast;
        case r'lunch': return SwapOrderRequestMealTypeEnum.lunch;
        case r'dinner': return SwapOrderRequestMealTypeEnum.dinner;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [SwapOrderRequestMealTypeEnumTypeTransformer] instance.
  static SwapOrderRequestMealTypeEnumTypeTransformer? _instance;
}


