//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class CancelOrderRequest {
  /// Returns a new [CancelOrderRequest] instance.
  CancelOrderRequest({
    required this.companyId,
    required this.reason,
    this.cancelDates = const [],
    this.mealType,
    this.cancelledBy,
  });

  /// Company id associated with this request
  int companyId;

  /// Reason for order cancellation
  String reason;

  /// Specific dates to cancel
  List<DateTime> cancelDates;

  /// Optional: Filter cancellation by specific meal type
  CancelOrderRequestMealTypeEnum? mealType;

  /// Who initiated the cancellation
  CancelOrderRequestCancelledByEnum? cancelledBy;

  @override
  bool operator ==(Object other) => identical(this, other) || other is CancelOrderRequest &&
    other.companyId == companyId &&
    other.reason == reason &&
    _deepEquality.equals(other.cancelDates, cancelDates) &&
    other.mealType == mealType &&
    other.cancelledBy == cancelledBy;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (companyId.hashCode) +
    (reason.hashCode) +
    (cancelDates.hashCode) +
    (mealType == null ? 0 : mealType!.hashCode) +
    (cancelledBy == null ? 0 : cancelledBy!.hashCode);

  @override
  String toString() => 'CancelOrderRequest[companyId=$companyId, reason=$reason, cancelDates=$cancelDates, mealType=$mealType, cancelledBy=$cancelledBy]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'company_id'] = this.companyId;
      json[r'reason'] = this.reason;
      json[r'cancel_dates'] = this.cancelDates;
    if (this.mealType != null) {
      json[r'meal_type'] = this.mealType;
    } else {
      json[r'meal_type'] = null;
    }
    if (this.cancelledBy != null) {
      json[r'cancelled_by'] = this.cancelledBy;
    } else {
      json[r'cancelled_by'] = null;
    }
    return json;
  }

  /// Returns a new [CancelOrderRequest] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static CancelOrderRequest? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "CancelOrderRequest[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "CancelOrderRequest[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return CancelOrderRequest(
        companyId: mapValueOfType<int>(json, r'company_id')!,
        reason: mapValueOfType<String>(json, r'reason')!,
        cancelDates: DateTime.listFromJson(json[r'cancel_dates']),
        mealType: CancelOrderRequestMealTypeEnum.fromJson(json[r'meal_type']),
        cancelledBy: CancelOrderRequestCancelledByEnum.fromJson(json[r'cancelled_by']),
      );
    }
    return null;
  }

  static List<CancelOrderRequest> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CancelOrderRequest>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CancelOrderRequest.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, CancelOrderRequest> mapFromJson(dynamic json) {
    final map = <String, CancelOrderRequest>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = CancelOrderRequest.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of CancelOrderRequest-objects as value to a dart map
  static Map<String, List<CancelOrderRequest>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<CancelOrderRequest>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = CancelOrderRequest.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'company_id',
    'reason',
    'cancel_dates',
  };
}

/// Optional: Filter cancellation by specific meal type
class CancelOrderRequestMealTypeEnum {
  /// Instantiate a new enum with the provided [value].
  const CancelOrderRequestMealTypeEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const breakfast = CancelOrderRequestMealTypeEnum._(r'breakfast');
  static const lunch = CancelOrderRequestMealTypeEnum._(r'lunch');
  static const dinner = CancelOrderRequestMealTypeEnum._(r'dinner');

  /// List of all possible values in this [enum][CancelOrderRequestMealTypeEnum].
  static const values = <CancelOrderRequestMealTypeEnum>[
    breakfast,
    lunch,
    dinner,
  ];

  static CancelOrderRequestMealTypeEnum? fromJson(dynamic value) => CancelOrderRequestMealTypeEnumTypeTransformer().decode(value);

  static List<CancelOrderRequestMealTypeEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CancelOrderRequestMealTypeEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CancelOrderRequestMealTypeEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [CancelOrderRequestMealTypeEnum] to String,
/// and [decode] dynamic data back to [CancelOrderRequestMealTypeEnum].
class CancelOrderRequestMealTypeEnumTypeTransformer {
  factory CancelOrderRequestMealTypeEnumTypeTransformer() => _instance ??= const CancelOrderRequestMealTypeEnumTypeTransformer._();

  const CancelOrderRequestMealTypeEnumTypeTransformer._();

  String encode(CancelOrderRequestMealTypeEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a CancelOrderRequestMealTypeEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  CancelOrderRequestMealTypeEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'breakfast': return CancelOrderRequestMealTypeEnum.breakfast;
        case r'lunch': return CancelOrderRequestMealTypeEnum.lunch;
        case r'dinner': return CancelOrderRequestMealTypeEnum.dinner;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [CancelOrderRequestMealTypeEnumTypeTransformer] instance.
  static CancelOrderRequestMealTypeEnumTypeTransformer? _instance;
}


/// Who initiated the cancellation
class CancelOrderRequestCancelledByEnum {
  /// Instantiate a new enum with the provided [value].
  const CancelOrderRequestCancelledByEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const customer = CancelOrderRequestCancelledByEnum._(r'customer');
  static const admin = CancelOrderRequestCancelledByEnum._(r'admin');
  static const system = CancelOrderRequestCancelledByEnum._(r'system');

  /// List of all possible values in this [enum][CancelOrderRequestCancelledByEnum].
  static const values = <CancelOrderRequestCancelledByEnum>[
    customer,
    admin,
    system,
  ];

  static CancelOrderRequestCancelledByEnum? fromJson(dynamic value) => CancelOrderRequestCancelledByEnumTypeTransformer().decode(value);

  static List<CancelOrderRequestCancelledByEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CancelOrderRequestCancelledByEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CancelOrderRequestCancelledByEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [CancelOrderRequestCancelledByEnum] to String,
/// and [decode] dynamic data back to [CancelOrderRequestCancelledByEnum].
class CancelOrderRequestCancelledByEnumTypeTransformer {
  factory CancelOrderRequestCancelledByEnumTypeTransformer() => _instance ??= const CancelOrderRequestCancelledByEnumTypeTransformer._();

  const CancelOrderRequestCancelledByEnumTypeTransformer._();

  String encode(CancelOrderRequestCancelledByEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a CancelOrderRequestCancelledByEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  CancelOrderRequestCancelledByEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'customer': return CancelOrderRequestCancelledByEnum.customer;
        case r'admin': return CancelOrderRequestCancelledByEnum.admin;
        case r'system': return CancelOrderRequestCancelledByEnum.system;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [CancelOrderRequestCancelledByEnumTypeTransformer] instance.
  static CancelOrderRequestCancelledByEnumTypeTransformer? _instance;
}


