//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class PerDateMealItem {
  /// Returns a new [PerDateMealItem] instance.
  PerDateMealItem({
    required this.productCode,
    required this.quantity,
  });

  int productCode;

  /// Minimum value: 1
  int quantity;

  @override
  bool operator ==(Object other) => identical(this, other) || other is PerDateMealItem &&
    other.productCode == productCode &&
    other.quantity == quantity;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (productCode.hashCode) +
    (quantity.hashCode);

  @override
  String toString() => 'PerDateMealItem[productCode=$productCode, quantity=$quantity]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'product_code'] = this.productCode;
      json[r'quantity'] = this.quantity;
    return json;
  }

  /// Returns a new [PerDateMealItem] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static PerDateMealItem? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "PerDateMealItem[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "PerDateMealItem[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return PerDateMealItem(
        productCode: mapValueOfType<int>(json, r'product_code')!,
        quantity: mapValueOfType<int>(json, r'quantity')!,
      );
    }
    return null;
  }

  static List<PerDateMealItem> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <PerDateMealItem>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = PerDateMealItem.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, PerDateMealItem> mapFromJson(dynamic json) {
    final map = <String, PerDateMealItem>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = PerDateMealItem.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of PerDateMealItem-objects as value to a dart map
  static Map<String, List<PerDateMealItem>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<PerDateMealItem>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = PerDateMealItem.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'product_code',
    'quantity',
  };
}

