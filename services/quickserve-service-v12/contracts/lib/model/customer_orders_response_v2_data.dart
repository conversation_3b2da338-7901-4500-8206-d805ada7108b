//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class CustomerOrdersResponseV2Data {
  /// Returns a new [CustomerOrdersResponseV2Data] instance.
  CustomerOrdersResponseV2Data({
    this.customer,
    this.summary,
    this.studentNames = const [],
    this.filters,
    this.orders,
    this.pagination,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  CustomerOrdersResponseV2DataCustomer? customer;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  CustomerOrdersResponseV2DataSummary? summary;

  List<String> studentNames;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  CustomerOrdersResponseV2DataFilters? filters;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  CustomerOrdersResponseV2DataOrders? orders;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  CustomerOrdersResponseV2DataPagination? pagination;

  @override
  bool operator ==(Object other) => identical(this, other) || other is CustomerOrdersResponseV2Data &&
    other.customer == customer &&
    other.summary == summary &&
    _deepEquality.equals(other.studentNames, studentNames) &&
    other.filters == filters &&
    other.orders == orders &&
    other.pagination == pagination;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (customer == null ? 0 : customer!.hashCode) +
    (summary == null ? 0 : summary!.hashCode) +
    (studentNames.hashCode) +
    (filters == null ? 0 : filters!.hashCode) +
    (orders == null ? 0 : orders!.hashCode) +
    (pagination == null ? 0 : pagination!.hashCode);

  @override
  String toString() => 'CustomerOrdersResponseV2Data[customer=$customer, summary=$summary, studentNames=$studentNames, filters=$filters, orders=$orders, pagination=$pagination]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.customer != null) {
      json[r'customer'] = this.customer;
    } else {
      json[r'customer'] = null;
    }
    if (this.summary != null) {
      json[r'summary'] = this.summary;
    } else {
      json[r'summary'] = null;
    }
      json[r'student_names'] = this.studentNames;
    if (this.filters != null) {
      json[r'filters'] = this.filters;
    } else {
      json[r'filters'] = null;
    }
    if (this.orders != null) {
      json[r'orders'] = this.orders;
    } else {
      json[r'orders'] = null;
    }
    if (this.pagination != null) {
      json[r'pagination'] = this.pagination;
    } else {
      json[r'pagination'] = null;
    }
    return json;
  }

  /// Returns a new [CustomerOrdersResponseV2Data] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static CustomerOrdersResponseV2Data? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "CustomerOrdersResponseV2Data[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "CustomerOrdersResponseV2Data[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return CustomerOrdersResponseV2Data(
        customer: CustomerOrdersResponseV2DataCustomer.fromJson(json[r'customer']),
        summary: CustomerOrdersResponseV2DataSummary.fromJson(json[r'summary']),
        studentNames: json[r'student_names'] is Iterable
            ? (json[r'student_names'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        filters: CustomerOrdersResponseV2DataFilters.fromJson(json[r'filters']),
        orders: CustomerOrdersResponseV2DataOrders.fromJson(json[r'orders']),
        pagination: CustomerOrdersResponseV2DataPagination.fromJson(json[r'pagination']),
      );
    }
    return null;
  }

  static List<CustomerOrdersResponseV2Data> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CustomerOrdersResponseV2Data>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CustomerOrdersResponseV2Data.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, CustomerOrdersResponseV2Data> mapFromJson(dynamic json) {
    final map = <String, CustomerOrdersResponseV2Data>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = CustomerOrdersResponseV2Data.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of CustomerOrdersResponseV2Data-objects as value to a dart map
  static Map<String, List<CustomerOrdersResponseV2Data>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<CustomerOrdersResponseV2Data>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = CustomerOrdersResponseV2Data.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

