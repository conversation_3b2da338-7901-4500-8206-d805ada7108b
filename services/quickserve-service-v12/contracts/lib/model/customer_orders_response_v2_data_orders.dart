//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class CustomerOrdersResponseV2DataOrders {
  /// Returns a new [CustomerOrdersResponseV2DataOrders] instance.
  CustomerOrdersResponseV2DataOrders({
    this.upcoming = const [],
    this.cancelled = const [],
    this.other = const [],
  });

  List<OrderListItem> upcoming;

  List<OrderListItem> cancelled;

  List<OrderListItem> other;

  @override
  bool operator ==(Object other) => identical(this, other) || other is CustomerOrdersResponseV2DataOrders &&
    _deepEquality.equals(other.upcoming, upcoming) &&
    _deepEquality.equals(other.cancelled, cancelled) &&
    _deepEquality.equals(other.other, other);

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (upcoming.hashCode) +
    (cancelled.hashCode) +
    (other.hashCode);

  @override
  String toString() => 'CustomerOrdersResponseV2DataOrders[upcoming=$upcoming, cancelled=$cancelled, other=$other]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'upcoming'] = this.upcoming;
      json[r'cancelled'] = this.cancelled;
      json[r'other'] = this.other;
    return json;
  }

  /// Returns a new [CustomerOrdersResponseV2DataOrders] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static CustomerOrdersResponseV2DataOrders? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "CustomerOrdersResponseV2DataOrders[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "CustomerOrdersResponseV2DataOrders[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return CustomerOrdersResponseV2DataOrders(
        upcoming: OrderListItem.listFromJson(json[r'upcoming']),
        cancelled: OrderListItem.listFromJson(json[r'cancelled']),
        other: OrderListItem.listFromJson(json[r'other']),
      );
    }
    return null;
  }

  static List<CustomerOrdersResponseV2DataOrders> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CustomerOrdersResponseV2DataOrders>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CustomerOrdersResponseV2DataOrders.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, CustomerOrdersResponseV2DataOrders> mapFromJson(dynamic json) {
    final map = <String, CustomerOrdersResponseV2DataOrders>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = CustomerOrdersResponseV2DataOrders.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of CustomerOrdersResponseV2DataOrders-objects as value to a dart map
  static Map<String, List<CustomerOrdersResponseV2DataOrders>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<CustomerOrdersResponseV2DataOrders>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = CustomerOrdersResponseV2DataOrders.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

