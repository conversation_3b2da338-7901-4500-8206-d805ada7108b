//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class CreateOrderRequest {
  /// Returns a new [CreateOrderRequest] instance.
  CreateOrderRequest({
    required this.customerId,
    required this.userId,
    required this.companyId,
    required this.unitId,
    required this.fkKitchenCode,
    this.customerName,
    this.customerEmail,
    this.customerPhone,
    required this.customerAddress,
    required this.locationCode,
    required this.locationName,
    required this.city,
    required this.cityName,
    this.foodPreference,
    this.paymentMethod = const CreateOrderRequestPaymentMethodEnum._('online'),
    this.isExpress = false,
    this.meals = const [],
    required this.startDate,
    this.selectedDays = const [],
    required this.subscriptionDays,
    this.mealsByDate = const [],
  });

  int customerId;

  int userId;

  int companyId;

  int unitId;

  int fkKitchenCode;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? customerName;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? customerEmail;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? customerPhone;

  String customerAddress;

  int locationCode;

  String locationName;

  int city;

  String cityName;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? foodPreference;

  /// Payment method: - online: Full payment via gateway - wallet: Use wallet balance (automatic partial payment if insufficient) 
  CreateOrderRequestPaymentMethodEnum paymentMethod;

  bool isExpress;

  List<MealCartItemShort> meals;

  DateTime startDate;

  List<int> selectedDays;

  /// Minimum value: 1
  /// Maximum value: 300
  int subscriptionDays;

  List<MealsByDateEntry> mealsByDate;

  @override
  bool operator ==(Object other) => identical(this, other) || other is CreateOrderRequest &&
    other.customerId == customerId &&
    other.userId == userId &&
    other.companyId == companyId &&
    other.unitId == unitId &&
    other.fkKitchenCode == fkKitchenCode &&
    other.customerName == customerName &&
    other.customerEmail == customerEmail &&
    other.customerPhone == customerPhone &&
    other.customerAddress == customerAddress &&
    other.locationCode == locationCode &&
    other.locationName == locationName &&
    other.city == city &&
    other.cityName == cityName &&
    other.foodPreference == foodPreference &&
    other.paymentMethod == paymentMethod &&
    other.isExpress == isExpress &&
    _deepEquality.equals(other.meals, meals) &&
    other.startDate == startDate &&
    _deepEquality.equals(other.selectedDays, selectedDays) &&
    other.subscriptionDays == subscriptionDays &&
    _deepEquality.equals(other.mealsByDate, mealsByDate);

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (customerId.hashCode) +
    (userId.hashCode) +
    (companyId.hashCode) +
    (unitId.hashCode) +
    (fkKitchenCode.hashCode) +
    (customerName == null ? 0 : customerName!.hashCode) +
    (customerEmail == null ? 0 : customerEmail!.hashCode) +
    (customerPhone == null ? 0 : customerPhone!.hashCode) +
    (customerAddress.hashCode) +
    (locationCode.hashCode) +
    (locationName.hashCode) +
    (city.hashCode) +
    (cityName.hashCode) +
    (foodPreference == null ? 0 : foodPreference!.hashCode) +
    (paymentMethod.hashCode) +
    (isExpress.hashCode) +
    (meals.hashCode) +
    (startDate.hashCode) +
    (selectedDays.hashCode) +
    (subscriptionDays.hashCode) +
    (mealsByDate.hashCode);

  @override
  String toString() => 'CreateOrderRequest[customerId=$customerId, userId=$userId, companyId=$companyId, unitId=$unitId, fkKitchenCode=$fkKitchenCode, customerName=$customerName, customerEmail=$customerEmail, customerPhone=$customerPhone, customerAddress=$customerAddress, locationCode=$locationCode, locationName=$locationName, city=$city, cityName=$cityName, foodPreference=$foodPreference, paymentMethod=$paymentMethod, isExpress=$isExpress, meals=$meals, startDate=$startDate, selectedDays=$selectedDays, subscriptionDays=$subscriptionDays, mealsByDate=$mealsByDate]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'customer_id'] = this.customerId;
      json[r'user_id'] = this.userId;
      json[r'company_id'] = this.companyId;
      json[r'unit_id'] = this.unitId;
      json[r'fk_kitchen_code'] = this.fkKitchenCode;
    if (this.customerName != null) {
      json[r'customer_name'] = this.customerName;
    } else {
      json[r'customer_name'] = null;
    }
    if (this.customerEmail != null) {
      json[r'customer_email'] = this.customerEmail;
    } else {
      json[r'customer_email'] = null;
    }
    if (this.customerPhone != null) {
      json[r'customer_phone'] = this.customerPhone;
    } else {
      json[r'customer_phone'] = null;
    }
      json[r'customer_address'] = this.customerAddress;
      json[r'location_code'] = this.locationCode;
      json[r'location_name'] = this.locationName;
      json[r'city'] = this.city;
      json[r'city_name'] = this.cityName;
    if (this.foodPreference != null) {
      json[r'food_preference'] = this.foodPreference;
    } else {
      json[r'food_preference'] = null;
    }
      json[r'payment_method'] = this.paymentMethod;
      json[r'is_express'] = this.isExpress;
      json[r'meals'] = this.meals;
      json[r'start_date'] = _dateFormatter.format(this.startDate.toUtc());
      json[r'selected_days'] = this.selectedDays;
      json[r'subscription_days'] = this.subscriptionDays;
      json[r'meals_by_date'] = this.mealsByDate;
    return json;
  }

  /// Returns a new [CreateOrderRequest] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static CreateOrderRequest? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "CreateOrderRequest[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "CreateOrderRequest[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return CreateOrderRequest(
        customerId: mapValueOfType<int>(json, r'customer_id')!,
        userId: mapValueOfType<int>(json, r'user_id')!,
        companyId: mapValueOfType<int>(json, r'company_id')!,
        unitId: mapValueOfType<int>(json, r'unit_id')!,
        fkKitchenCode: mapValueOfType<int>(json, r'fk_kitchen_code')!,
        customerName: mapValueOfType<String>(json, r'customer_name'),
        customerEmail: mapValueOfType<String>(json, r'customer_email'),
        customerPhone: mapValueOfType<String>(json, r'customer_phone'),
        customerAddress: mapValueOfType<String>(json, r'customer_address')!,
        locationCode: mapValueOfType<int>(json, r'location_code')!,
        locationName: mapValueOfType<String>(json, r'location_name')!,
        city: mapValueOfType<int>(json, r'city')!,
        cityName: mapValueOfType<String>(json, r'city_name')!,
        foodPreference: mapValueOfType<String>(json, r'food_preference'),
        paymentMethod: CreateOrderRequestPaymentMethodEnum.fromJson(json[r'payment_method']) ?? 'online',
        isExpress: mapValueOfType<bool>(json, r'is_express') ?? false,
        meals: MealCartItemShort.listFromJson(json[r'meals']),
        startDate: mapDateTime(json, r'start_date', r'')!,
        selectedDays: json[r'selected_days'] is Iterable
            ? (json[r'selected_days'] as Iterable).cast<int>().toList(growable: false)
            : const [],
        subscriptionDays: mapValueOfType<int>(json, r'subscription_days')!,
        mealsByDate: MealsByDateEntry.listFromJson(json[r'meals_by_date']),
      );
    }
    return null;
  }

  static List<CreateOrderRequest> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CreateOrderRequest>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CreateOrderRequest.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, CreateOrderRequest> mapFromJson(dynamic json) {
    final map = <String, CreateOrderRequest>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = CreateOrderRequest.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of CreateOrderRequest-objects as value to a dart map
  static Map<String, List<CreateOrderRequest>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<CreateOrderRequest>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = CreateOrderRequest.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'customer_id',
    'user_id',
    'company_id',
    'unit_id',
    'fk_kitchen_code',
    'customer_address',
    'location_code',
    'location_name',
    'city',
    'city_name',
    'meals',
    'start_date',
    'selected_days',
    'subscription_days',
    'meals_by_date',
  };
}

/// Payment method: - online: Full payment via gateway - wallet: Use wallet balance (automatic partial payment if insufficient) 
class CreateOrderRequestPaymentMethodEnum {
  /// Instantiate a new enum with the provided [value].
  const CreateOrderRequestPaymentMethodEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const online = CreateOrderRequestPaymentMethodEnum._(r'online');
  static const wallet = CreateOrderRequestPaymentMethodEnum._(r'wallet');

  /// List of all possible values in this [enum][CreateOrderRequestPaymentMethodEnum].
  static const values = <CreateOrderRequestPaymentMethodEnum>[
    online,
    wallet,
  ];

  static CreateOrderRequestPaymentMethodEnum? fromJson(dynamic value) => CreateOrderRequestPaymentMethodEnumTypeTransformer().decode(value);

  static List<CreateOrderRequestPaymentMethodEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CreateOrderRequestPaymentMethodEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CreateOrderRequestPaymentMethodEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [CreateOrderRequestPaymentMethodEnum] to String,
/// and [decode] dynamic data back to [CreateOrderRequestPaymentMethodEnum].
class CreateOrderRequestPaymentMethodEnumTypeTransformer {
  factory CreateOrderRequestPaymentMethodEnumTypeTransformer() => _instance ??= const CreateOrderRequestPaymentMethodEnumTypeTransformer._();

  const CreateOrderRequestPaymentMethodEnumTypeTransformer._();

  String encode(CreateOrderRequestPaymentMethodEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a CreateOrderRequestPaymentMethodEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  CreateOrderRequestPaymentMethodEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'online': return CreateOrderRequestPaymentMethodEnum.online;
        case r'wallet': return CreateOrderRequestPaymentMethodEnum.wallet;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [CreateOrderRequestPaymentMethodEnumTypeTransformer] instance.
  static CreateOrderRequestPaymentMethodEnumTypeTransformer? _instance;
}


