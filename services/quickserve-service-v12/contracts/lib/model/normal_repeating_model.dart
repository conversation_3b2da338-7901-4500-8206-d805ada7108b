//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class NormalRepeatingModel {
  /// Returns a new [NormalRepeatingModel] instance.
  NormalRepeatingModel({
    this.meals = const [],
    required this.startDate,
    this.selectedDays = const [],
    required this.subscriptionDays,
  });

  List<MealCartItemShort> meals;

  DateTime startDate;

  List<int> selectedDays;

  /// Minimum value: 1
  /// Maximum value: 300
  int subscriptionDays;

  @override
  bool operator ==(Object other) => identical(this, other) || other is NormalRepeatingModel &&
    _deepEquality.equals(other.meals, meals) &&
    other.startDate == startDate &&
    _deepEquality.equals(other.selectedDays, selectedDays) &&
    other.subscriptionDays == subscriptionDays;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (meals.hashCode) +
    (startDate.hashCode) +
    (selectedDays.hashCode) +
    (subscriptionDays.hashCode);

  @override
  String toString() => 'NormalRepeatingModel[meals=$meals, startDate=$startDate, selectedDays=$selectedDays, subscriptionDays=$subscriptionDays]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'meals'] = this.meals;
      json[r'start_date'] = _dateFormatter.format(this.startDate.toUtc());
      json[r'selected_days'] = this.selectedDays;
      json[r'subscription_days'] = this.subscriptionDays;
    return json;
  }

  /// Returns a new [NormalRepeatingModel] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static NormalRepeatingModel? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "NormalRepeatingModel[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "NormalRepeatingModel[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return NormalRepeatingModel(
        meals: MealCartItemShort.listFromJson(json[r'meals']),
        startDate: mapDateTime(json, r'start_date', r'')!,
        selectedDays: json[r'selected_days'] is Iterable
            ? (json[r'selected_days'] as Iterable).cast<int>().toList(growable: false)
            : const [],
        subscriptionDays: mapValueOfType<int>(json, r'subscription_days')!,
      );
    }
    return null;
  }

  static List<NormalRepeatingModel> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <NormalRepeatingModel>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = NormalRepeatingModel.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, NormalRepeatingModel> mapFromJson(dynamic json) {
    final map = <String, NormalRepeatingModel>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = NormalRepeatingModel.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of NormalRepeatingModel-objects as value to a dart map
  static Map<String, List<NormalRepeatingModel>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<NormalRepeatingModel>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = NormalRepeatingModel.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'meals',
    'start_date',
    'selected_days',
    'subscription_days',
  };
}

