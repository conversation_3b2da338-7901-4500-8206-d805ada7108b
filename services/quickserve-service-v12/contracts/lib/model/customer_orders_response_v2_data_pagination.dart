//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class CustomerOrdersResponseV2DataPagination {
  /// Returns a new [CustomerOrdersResponseV2DataPagination] instance.
  CustomerOrdersResponseV2DataPagination({
    this.upcoming,
    this.cancelled,
    this.other,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  PaginationMeta? upcoming;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  PaginationMeta? cancelled;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  PaginationMeta? other;

  @override
  bool operator ==(Object other) => identical(this, other) || other is CustomerOrdersResponseV2DataPagination &&
    other.upcoming == upcoming &&
    other.cancelled == cancelled &&
    other.other == other;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (upcoming == null ? 0 : upcoming!.hashCode) +
    (cancelled == null ? 0 : cancelled!.hashCode) +
    (other == null ? 0 : other!.hashCode);

  @override
  String toString() => 'CustomerOrdersResponseV2DataPagination[upcoming=$upcoming, cancelled=$cancelled, other=$other]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.upcoming != null) {
      json[r'upcoming'] = this.upcoming;
    } else {
      json[r'upcoming'] = null;
    }
    if (this.cancelled != null) {
      json[r'cancelled'] = this.cancelled;
    } else {
      json[r'cancelled'] = null;
    }
    if (this.other != null) {
      json[r'other'] = this.other;
    } else {
      json[r'other'] = null;
    }
    return json;
  }

  /// Returns a new [CustomerOrdersResponseV2DataPagination] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static CustomerOrdersResponseV2DataPagination? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "CustomerOrdersResponseV2DataPagination[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "CustomerOrdersResponseV2DataPagination[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return CustomerOrdersResponseV2DataPagination(
        upcoming: PaginationMeta.fromJson(json[r'upcoming']),
        cancelled: PaginationMeta.fromJson(json[r'cancelled']),
        other: PaginationMeta.fromJson(json[r'other']),
      );
    }
    return null;
  }

  static List<CustomerOrdersResponseV2DataPagination> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CustomerOrdersResponseV2DataPagination>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CustomerOrdersResponseV2DataPagination.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, CustomerOrdersResponseV2DataPagination> mapFromJson(dynamic json) {
    final map = <String, CustomerOrdersResponseV2DataPagination>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = CustomerOrdersResponseV2DataPagination.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of CustomerOrdersResponseV2DataPagination-objects as value to a dart map
  static Map<String, List<CustomerOrdersResponseV2DataPagination>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<CustomerOrdersResponseV2DataPagination>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = CustomerOrdersResponseV2DataPagination.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

