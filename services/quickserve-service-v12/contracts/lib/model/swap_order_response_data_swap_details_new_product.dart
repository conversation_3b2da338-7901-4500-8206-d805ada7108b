//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SwapOrderResponseDataSwapDetailsNewProduct {
  /// Returns a new [SwapOrderResponseDataSwapDetailsNewProduct] instance.
  SwapOrderResponseDataSwapDetailsNewProduct({
    this.code,
    this.name,
    this.price,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? code;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? name;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? price;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SwapOrderResponseDataSwapDetailsNewProduct &&
    other.code == code &&
    other.name == name &&
    other.price == price;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (code == null ? 0 : code!.hashCode) +
    (name == null ? 0 : name!.hashCode) +
    (price == null ? 0 : price!.hashCode);

  @override
  String toString() => 'SwapOrderResponseDataSwapDetailsNewProduct[code=$code, name=$name, price=$price]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.code != null) {
      json[r'code'] = this.code;
    } else {
      json[r'code'] = null;
    }
    if (this.name != null) {
      json[r'name'] = this.name;
    } else {
      json[r'name'] = null;
    }
    if (this.price != null) {
      json[r'price'] = this.price;
    } else {
      json[r'price'] = null;
    }
    return json;
  }

  /// Returns a new [SwapOrderResponseDataSwapDetailsNewProduct] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SwapOrderResponseDataSwapDetailsNewProduct? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SwapOrderResponseDataSwapDetailsNewProduct[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SwapOrderResponseDataSwapDetailsNewProduct[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SwapOrderResponseDataSwapDetailsNewProduct(
        code: mapValueOfType<int>(json, r'code'),
        name: mapValueOfType<String>(json, r'name'),
        price: num.parse('${json[r'price']}'),
      );
    }
    return null;
  }

  static List<SwapOrderResponseDataSwapDetailsNewProduct> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SwapOrderResponseDataSwapDetailsNewProduct>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SwapOrderResponseDataSwapDetailsNewProduct.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SwapOrderResponseDataSwapDetailsNewProduct> mapFromJson(dynamic json) {
    final map = <String, SwapOrderResponseDataSwapDetailsNewProduct>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SwapOrderResponseDataSwapDetailsNewProduct.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SwapOrderResponseDataSwapDetailsNewProduct-objects as value to a dart map
  static Map<String, List<SwapOrderResponseDataSwapDetailsNewProduct>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SwapOrderResponseDataSwapDetailsNewProduct>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SwapOrderResponseDataSwapDetailsNewProduct.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

