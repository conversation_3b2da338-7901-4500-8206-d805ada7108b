//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class CreateOrderResponseData {
  /// Returns a new [CreateOrderResponseData] instance.
  CreateOrderResponseData({
    this.primaryOrderNo,
    this.paymentServiceTransactionId,
    this.tempPreOrderId,
    this.express,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? primaryOrderNo;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? paymentServiceTransactionId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? tempPreOrderId;

  CreateOrderResponseDataExpress? express;

  @override
  bool operator ==(Object other) => identical(this, other) || other is CreateOrderResponseData &&
    other.primaryOrderNo == primaryOrderNo &&
    other.paymentServiceTransactionId == paymentServiceTransactionId &&
    other.tempPreOrderId == tempPreOrderId &&
    other.express == express;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (primaryOrderNo == null ? 0 : primaryOrderNo!.hashCode) +
    (paymentServiceTransactionId == null ? 0 : paymentServiceTransactionId!.hashCode) +
    (tempPreOrderId == null ? 0 : tempPreOrderId!.hashCode) +
    (express == null ? 0 : express!.hashCode);

  @override
  String toString() => 'CreateOrderResponseData[primaryOrderNo=$primaryOrderNo, paymentServiceTransactionId=$paymentServiceTransactionId, tempPreOrderId=$tempPreOrderId, express=$express]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.primaryOrderNo != null) {
      json[r'primary_order_no'] = this.primaryOrderNo;
    } else {
      json[r'primary_order_no'] = null;
    }
    if (this.paymentServiceTransactionId != null) {
      json[r'payment_service_transaction_id'] = this.paymentServiceTransactionId;
    } else {
      json[r'payment_service_transaction_id'] = null;
    }
    if (this.tempPreOrderId != null) {
      json[r'temp_pre_order_id'] = this.tempPreOrderId;
    } else {
      json[r'temp_pre_order_id'] = null;
    }
    if (this.express != null) {
      json[r'express'] = this.express;
    } else {
      json[r'express'] = null;
    }
    return json;
  }

  /// Returns a new [CreateOrderResponseData] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static CreateOrderResponseData? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "CreateOrderResponseData[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "CreateOrderResponseData[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return CreateOrderResponseData(
        primaryOrderNo: mapValueOfType<String>(json, r'primary_order_no'),
        paymentServiceTransactionId: mapValueOfType<String>(json, r'payment_service_transaction_id'),
        tempPreOrderId: mapValueOfType<int>(json, r'temp_pre_order_id'),
        express: CreateOrderResponseDataExpress.fromJson(json[r'express']),
      );
    }
    return null;
  }

  static List<CreateOrderResponseData> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CreateOrderResponseData>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CreateOrderResponseData.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, CreateOrderResponseData> mapFromJson(dynamic json) {
    final map = <String, CreateOrderResponseData>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = CreateOrderResponseData.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of CreateOrderResponseData-objects as value to a dart map
  static Map<String, List<CreateOrderResponseData>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<CreateOrderResponseData>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = CreateOrderResponseData.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

