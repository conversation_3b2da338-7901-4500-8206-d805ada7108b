//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class CreateOrderResponseDataExpress {
  /// Returns a new [CreateOrderResponseDataExpress] instance.
  CreateOrderResponseDataExpress({
    this.evaluated,
    this.withinWindow,
    this.cutoffTime,
    this.extendedEndTime,
    this.extraDeliveryCharge,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? evaluated;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? withinWindow;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? cutoffTime;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? extendedEndTime;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? extraDeliveryCharge;

  @override
  bool operator ==(Object other) => identical(this, other) || other is CreateOrderResponseDataExpress &&
    other.evaluated == evaluated &&
    other.withinWindow == withinWindow &&
    other.cutoffTime == cutoffTime &&
    other.extendedEndTime == extendedEndTime &&
    other.extraDeliveryCharge == extraDeliveryCharge;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (evaluated == null ? 0 : evaluated!.hashCode) +
    (withinWindow == null ? 0 : withinWindow!.hashCode) +
    (cutoffTime == null ? 0 : cutoffTime!.hashCode) +
    (extendedEndTime == null ? 0 : extendedEndTime!.hashCode) +
    (extraDeliveryCharge == null ? 0 : extraDeliveryCharge!.hashCode);

  @override
  String toString() => 'CreateOrderResponseDataExpress[evaluated=$evaluated, withinWindow=$withinWindow, cutoffTime=$cutoffTime, extendedEndTime=$extendedEndTime, extraDeliveryCharge=$extraDeliveryCharge]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.evaluated != null) {
      json[r'evaluated'] = this.evaluated;
    } else {
      json[r'evaluated'] = null;
    }
    if (this.withinWindow != null) {
      json[r'within_window'] = this.withinWindow;
    } else {
      json[r'within_window'] = null;
    }
    if (this.cutoffTime != null) {
      json[r'cutoff_time'] = this.cutoffTime;
    } else {
      json[r'cutoff_time'] = null;
    }
    if (this.extendedEndTime != null) {
      json[r'extended_end_time'] = this.extendedEndTime;
    } else {
      json[r'extended_end_time'] = null;
    }
    if (this.extraDeliveryCharge != null) {
      json[r'extra_delivery_charge'] = this.extraDeliveryCharge;
    } else {
      json[r'extra_delivery_charge'] = null;
    }
    return json;
  }

  /// Returns a new [CreateOrderResponseDataExpress] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static CreateOrderResponseDataExpress? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "CreateOrderResponseDataExpress[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "CreateOrderResponseDataExpress[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return CreateOrderResponseDataExpress(
        evaluated: mapValueOfType<bool>(json, r'evaluated'),
        withinWindow: mapValueOfType<bool>(json, r'within_window'),
        cutoffTime: mapValueOfType<String>(json, r'cutoff_time'),
        extendedEndTime: mapValueOfType<String>(json, r'extended_end_time'),
        extraDeliveryCharge: num.parse('${json[r'extra_delivery_charge']}'),
      );
    }
    return null;
  }

  static List<CreateOrderResponseDataExpress> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CreateOrderResponseDataExpress>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CreateOrderResponseDataExpress.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, CreateOrderResponseDataExpress> mapFromJson(dynamic json) {
    final map = <String, CreateOrderResponseDataExpress>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = CreateOrderResponseDataExpress.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of CreateOrderResponseDataExpress-objects as value to a dart map
  static Map<String, List<CreateOrderResponseDataExpress>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<CreateOrderResponseDataExpress>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = CreateOrderResponseDataExpress.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

