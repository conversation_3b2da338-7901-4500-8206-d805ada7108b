//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class CancelOrderResponseData {
  /// Returns a new [CancelOrderResponseData] instance.
  CancelOrderResponseData({
    this.cancelledOrders,
    this.cancelledOrderIds = const [],
    this.totalRefundAmount,
    this.cancellationDetails,
  });

  /// Number of orders cancelled
  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? cancelledOrders;

  /// List of cancelled order IDs
  List<int> cancelledOrderIds;

  /// Total refund amount credited to wallet
  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? totalRefundAmount;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  CancelOrderResponseDataCancellationDetails? cancellationDetails;

  @override
  bool operator ==(Object other) => identical(this, other) || other is CancelOrderResponseData &&
    other.cancelledOrders == cancelledOrders &&
    _deepEquality.equals(other.cancelledOrderIds, cancelledOrderIds) &&
    other.totalRefundAmount == totalRefundAmount &&
    other.cancellationDetails == cancellationDetails;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (cancelledOrders == null ? 0 : cancelledOrders!.hashCode) +
    (cancelledOrderIds.hashCode) +
    (totalRefundAmount == null ? 0 : totalRefundAmount!.hashCode) +
    (cancellationDetails == null ? 0 : cancellationDetails!.hashCode);

  @override
  String toString() => 'CancelOrderResponseData[cancelledOrders=$cancelledOrders, cancelledOrderIds=$cancelledOrderIds, totalRefundAmount=$totalRefundAmount, cancellationDetails=$cancellationDetails]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.cancelledOrders != null) {
      json[r'cancelled_orders'] = this.cancelledOrders;
    } else {
      json[r'cancelled_orders'] = null;
    }
      json[r'cancelled_order_ids'] = this.cancelledOrderIds;
    if (this.totalRefundAmount != null) {
      json[r'total_refund_amount'] = this.totalRefundAmount;
    } else {
      json[r'total_refund_amount'] = null;
    }
    if (this.cancellationDetails != null) {
      json[r'cancellation_details'] = this.cancellationDetails;
    } else {
      json[r'cancellation_details'] = null;
    }
    return json;
  }

  /// Returns a new [CancelOrderResponseData] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static CancelOrderResponseData? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "CancelOrderResponseData[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "CancelOrderResponseData[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return CancelOrderResponseData(
        cancelledOrders: mapValueOfType<int>(json, r'cancelled_orders'),
        cancelledOrderIds: json[r'cancelled_order_ids'] is Iterable
            ? (json[r'cancelled_order_ids'] as Iterable).cast<int>().toList(growable: false)
            : const [],
        totalRefundAmount: num.parse('${json[r'total_refund_amount']}'),
        cancellationDetails: CancelOrderResponseDataCancellationDetails.fromJson(json[r'cancellation_details']),
      );
    }
    return null;
  }

  static List<CancelOrderResponseData> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CancelOrderResponseData>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CancelOrderResponseData.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, CancelOrderResponseData> mapFromJson(dynamic json) {
    final map = <String, CancelOrderResponseData>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = CancelOrderResponseData.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of CancelOrderResponseData-objects as value to a dart map
  static Map<String, List<CancelOrderResponseData>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<CancelOrderResponseData>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = CancelOrderResponseData.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

