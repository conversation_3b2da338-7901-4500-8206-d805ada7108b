//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class ApplyCouponRequest {
  /// Returns a new [ApplyCouponRequest] instance.
  ApplyCouponRequest({
    required this.companyId,
    required this.orderNo,
    required this.promoCode,
  });

  /// Company id associated with this request
  int companyId;

  /// Order number to apply coupon
  String orderNo;

  /// Promo code to apply
  String promoCode;

  @override
  bool operator ==(Object other) => identical(this, other) || other is ApplyCouponRequest &&
    other.companyId == companyId &&
    other.orderNo == orderNo &&
    other.promoCode == promoCode;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (companyId.hashCode) +
    (orderNo.hashCode) +
    (promoCode.hashCode);

  @override
  String toString() => 'ApplyCouponRequest[companyId=$companyId, orderNo=$orderNo, promoCode=$promoCode]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'company_id'] = this.companyId;
      json[r'order_no'] = this.orderNo;
      json[r'promo_code'] = this.promoCode;
    return json;
  }

  /// Returns a new [ApplyCouponRequest] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static ApplyCouponRequest? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "ApplyCouponRequest[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "ApplyCouponRequest[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return ApplyCouponRequest(
        companyId: mapValueOfType<int>(json, r'company_id')!,
        orderNo: mapValueOfType<String>(json, r'order_no')!,
        promoCode: mapValueOfType<String>(json, r'promo_code')!,
      );
    }
    return null;
  }

  static List<ApplyCouponRequest> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ApplyCouponRequest>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ApplyCouponRequest.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, ApplyCouponRequest> mapFromJson(dynamic json) {
    final map = <String, ApplyCouponRequest>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ApplyCouponRequest.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of ApplyCouponRequest-objects as value to a dart map
  static Map<String, List<ApplyCouponRequest>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<ApplyCouponRequest>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = ApplyCouponRequest.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'company_id',
    'order_no',
    'promo_code',
  };
}

