//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SwapOrderResponseData {
  /// Returns a new [SwapOrderResponseData] instance.
  SwapOrderResponseData({
    this.orderId,
    this.orderNo,
    this.orderDate,
    this.swapDetails,
    this.reason,
  });

  /// Internal order ID
  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? orderId;

  /// Order number that was swapped
  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? orderNo;

  /// Date of the swapped order
  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DateTime? orderDate;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  SwapOrderResponseDataSwapDetails? swapDetails;

  /// Reason provided for the swap
  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? reason;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SwapOrderResponseData &&
    other.orderId == orderId &&
    other.orderNo == orderNo &&
    other.orderDate == orderDate &&
    other.swapDetails == swapDetails &&
    other.reason == reason;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (orderId == null ? 0 : orderId!.hashCode) +
    (orderNo == null ? 0 : orderNo!.hashCode) +
    (orderDate == null ? 0 : orderDate!.hashCode) +
    (swapDetails == null ? 0 : swapDetails!.hashCode) +
    (reason == null ? 0 : reason!.hashCode);

  @override
  String toString() => 'SwapOrderResponseData[orderId=$orderId, orderNo=$orderNo, orderDate=$orderDate, swapDetails=$swapDetails, reason=$reason]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.orderId != null) {
      json[r'order_id'] = this.orderId;
    } else {
      json[r'order_id'] = null;
    }
    if (this.orderNo != null) {
      json[r'order_no'] = this.orderNo;
    } else {
      json[r'order_no'] = null;
    }
    if (this.orderDate != null) {
      json[r'order_date'] = _dateFormatter.format(this.orderDate!.toUtc());
    } else {
      json[r'order_date'] = null;
    }
    if (this.swapDetails != null) {
      json[r'swap_details'] = this.swapDetails;
    } else {
      json[r'swap_details'] = null;
    }
    if (this.reason != null) {
      json[r'reason'] = this.reason;
    } else {
      json[r'reason'] = null;
    }
    return json;
  }

  /// Returns a new [SwapOrderResponseData] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SwapOrderResponseData? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SwapOrderResponseData[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SwapOrderResponseData[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SwapOrderResponseData(
        orderId: mapValueOfType<int>(json, r'order_id'),
        orderNo: mapValueOfType<String>(json, r'order_no'),
        orderDate: mapDateTime(json, r'order_date', r''),
        swapDetails: SwapOrderResponseDataSwapDetails.fromJson(json[r'swap_details']),
        reason: mapValueOfType<String>(json, r'reason'),
      );
    }
    return null;
  }

  static List<SwapOrderResponseData> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SwapOrderResponseData>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SwapOrderResponseData.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SwapOrderResponseData> mapFromJson(dynamic json) {
    final map = <String, SwapOrderResponseData>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SwapOrderResponseData.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SwapOrderResponseData-objects as value to a dart map
  static Map<String, List<SwapOrderResponseData>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SwapOrderResponseData>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SwapOrderResponseData.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

