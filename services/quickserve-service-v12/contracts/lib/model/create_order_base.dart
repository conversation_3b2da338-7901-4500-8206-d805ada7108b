//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class CreateOrderBase {
  /// Returns a new [CreateOrderBase] instance.
  CreateOrderBase({
    this.customerId,
    this.userId,
    this.companyId,
    this.unitId,
    this.fkKitchenCode,
    this.customerName,
    this.customerEmail,
    this.customerPhone,
    this.customerAddress,
    this.locationCode,
    this.locationName,
    this.city,
    this.cityName,
    this.foodPreference,
    this.paymentMethod = const CreateOrderBasePaymentMethodEnum._('online'),
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? customerId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? userId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? companyId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? unitId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? fkKitchenCode;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? customerName;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? customerEmail;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? customerPhone;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? customerAddress;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? locationCode;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? locationName;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? city;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? cityName;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? foodPreference;

  /// Payment method: - online: Full payment via gateway - wallet: Use wallet balance (automatic partial payment if insufficient) 
  CreateOrderBasePaymentMethodEnum paymentMethod;

  @override
  bool operator ==(Object other) => identical(this, other) || other is CreateOrderBase &&
    other.customerId == customerId &&
    other.userId == userId &&
    other.companyId == companyId &&
    other.unitId == unitId &&
    other.fkKitchenCode == fkKitchenCode &&
    other.customerName == customerName &&
    other.customerEmail == customerEmail &&
    other.customerPhone == customerPhone &&
    other.customerAddress == customerAddress &&
    other.locationCode == locationCode &&
    other.locationName == locationName &&
    other.city == city &&
    other.cityName == cityName &&
    other.foodPreference == foodPreference &&
    other.paymentMethod == paymentMethod;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (customerId == null ? 0 : customerId!.hashCode) +
    (userId == null ? 0 : userId!.hashCode) +
    (companyId == null ? 0 : companyId!.hashCode) +
    (unitId == null ? 0 : unitId!.hashCode) +
    (fkKitchenCode == null ? 0 : fkKitchenCode!.hashCode) +
    (customerName == null ? 0 : customerName!.hashCode) +
    (customerEmail == null ? 0 : customerEmail!.hashCode) +
    (customerPhone == null ? 0 : customerPhone!.hashCode) +
    (customerAddress == null ? 0 : customerAddress!.hashCode) +
    (locationCode == null ? 0 : locationCode!.hashCode) +
    (locationName == null ? 0 : locationName!.hashCode) +
    (city == null ? 0 : city!.hashCode) +
    (cityName == null ? 0 : cityName!.hashCode) +
    (foodPreference == null ? 0 : foodPreference!.hashCode) +
    (paymentMethod.hashCode);

  @override
  String toString() => 'CreateOrderBase[customerId=$customerId, userId=$userId, companyId=$companyId, unitId=$unitId, fkKitchenCode=$fkKitchenCode, customerName=$customerName, customerEmail=$customerEmail, customerPhone=$customerPhone, customerAddress=$customerAddress, locationCode=$locationCode, locationName=$locationName, city=$city, cityName=$cityName, foodPreference=$foodPreference, paymentMethod=$paymentMethod]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.customerId != null) {
      json[r'customer_id'] = this.customerId;
    } else {
      json[r'customer_id'] = null;
    }
    if (this.userId != null) {
      json[r'user_id'] = this.userId;
    } else {
      json[r'user_id'] = null;
    }
    if (this.companyId != null) {
      json[r'company_id'] = this.companyId;
    } else {
      json[r'company_id'] = null;
    }
    if (this.unitId != null) {
      json[r'unit_id'] = this.unitId;
    } else {
      json[r'unit_id'] = null;
    }
    if (this.fkKitchenCode != null) {
      json[r'fk_kitchen_code'] = this.fkKitchenCode;
    } else {
      json[r'fk_kitchen_code'] = null;
    }
    if (this.customerName != null) {
      json[r'customer_name'] = this.customerName;
    } else {
      json[r'customer_name'] = null;
    }
    if (this.customerEmail != null) {
      json[r'customer_email'] = this.customerEmail;
    } else {
      json[r'customer_email'] = null;
    }
    if (this.customerPhone != null) {
      json[r'customer_phone'] = this.customerPhone;
    } else {
      json[r'customer_phone'] = null;
    }
    if (this.customerAddress != null) {
      json[r'customer_address'] = this.customerAddress;
    } else {
      json[r'customer_address'] = null;
    }
    if (this.locationCode != null) {
      json[r'location_code'] = this.locationCode;
    } else {
      json[r'location_code'] = null;
    }
    if (this.locationName != null) {
      json[r'location_name'] = this.locationName;
    } else {
      json[r'location_name'] = null;
    }
    if (this.city != null) {
      json[r'city'] = this.city;
    } else {
      json[r'city'] = null;
    }
    if (this.cityName != null) {
      json[r'city_name'] = this.cityName;
    } else {
      json[r'city_name'] = null;
    }
    if (this.foodPreference != null) {
      json[r'food_preference'] = this.foodPreference;
    } else {
      json[r'food_preference'] = null;
    }
      json[r'payment_method'] = this.paymentMethod;
    return json;
  }

  /// Returns a new [CreateOrderBase] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static CreateOrderBase? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "CreateOrderBase[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "CreateOrderBase[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return CreateOrderBase(
        customerId: mapValueOfType<int>(json, r'customer_id'),
        userId: mapValueOfType<int>(json, r'user_id'),
        companyId: mapValueOfType<int>(json, r'company_id'),
        unitId: mapValueOfType<int>(json, r'unit_id'),
        fkKitchenCode: mapValueOfType<int>(json, r'fk_kitchen_code'),
        customerName: mapValueOfType<String>(json, r'customer_name'),
        customerEmail: mapValueOfType<String>(json, r'customer_email'),
        customerPhone: mapValueOfType<String>(json, r'customer_phone'),
        customerAddress: mapValueOfType<String>(json, r'customer_address'),
        locationCode: mapValueOfType<int>(json, r'location_code'),
        locationName: mapValueOfType<String>(json, r'location_name'),
        city: mapValueOfType<int>(json, r'city'),
        cityName: mapValueOfType<String>(json, r'city_name'),
        foodPreference: mapValueOfType<String>(json, r'food_preference'),
        paymentMethod: CreateOrderBasePaymentMethodEnum.fromJson(json[r'payment_method']) ?? 'online',
      );
    }
    return null;
  }

  static List<CreateOrderBase> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CreateOrderBase>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CreateOrderBase.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, CreateOrderBase> mapFromJson(dynamic json) {
    final map = <String, CreateOrderBase>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = CreateOrderBase.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of CreateOrderBase-objects as value to a dart map
  static Map<String, List<CreateOrderBase>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<CreateOrderBase>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = CreateOrderBase.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

/// Payment method: - online: Full payment via gateway - wallet: Use wallet balance (automatic partial payment if insufficient) 
class CreateOrderBasePaymentMethodEnum {
  /// Instantiate a new enum with the provided [value].
  const CreateOrderBasePaymentMethodEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const online = CreateOrderBasePaymentMethodEnum._(r'online');
  static const wallet = CreateOrderBasePaymentMethodEnum._(r'wallet');

  /// List of all possible values in this [enum][CreateOrderBasePaymentMethodEnum].
  static const values = <CreateOrderBasePaymentMethodEnum>[
    online,
    wallet,
  ];

  static CreateOrderBasePaymentMethodEnum? fromJson(dynamic value) => CreateOrderBasePaymentMethodEnumTypeTransformer().decode(value);

  static List<CreateOrderBasePaymentMethodEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CreateOrderBasePaymentMethodEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CreateOrderBasePaymentMethodEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [CreateOrderBasePaymentMethodEnum] to String,
/// and [decode] dynamic data back to [CreateOrderBasePaymentMethodEnum].
class CreateOrderBasePaymentMethodEnumTypeTransformer {
  factory CreateOrderBasePaymentMethodEnumTypeTransformer() => _instance ??= const CreateOrderBasePaymentMethodEnumTypeTransformer._();

  const CreateOrderBasePaymentMethodEnumTypeTransformer._();

  String encode(CreateOrderBasePaymentMethodEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a CreateOrderBasePaymentMethodEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  CreateOrderBasePaymentMethodEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'online': return CreateOrderBasePaymentMethodEnum.online;
        case r'wallet': return CreateOrderBasePaymentMethodEnum.wallet;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [CreateOrderBasePaymentMethodEnumTypeTransformer] instance.
  static CreateOrderBasePaymentMethodEnumTypeTransformer? _instance;
}


