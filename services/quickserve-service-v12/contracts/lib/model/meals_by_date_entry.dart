//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class MealsByDateEntry {
  /// Returns a new [MealsByDateEntry] instance.
  MealsByDateEntry({
    required this.date,
    this.meals = const [],
  });

  /// Delivery date (YYYY-MM-DD), must be today or a future date.
  DateTime date;

  List<PerDateMealItem> meals;

  @override
  bool operator ==(Object other) => identical(this, other) || other is MealsByDateEntry &&
    other.date == date &&
    _deepEquality.equals(other.meals, meals);

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (date.hashCode) +
    (meals.hashCode);

  @override
  String toString() => 'MealsByDateEntry[date=$date, meals=$meals]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'date'] = _dateFormatter.format(this.date.toUtc());
      json[r'meals'] = this.meals;
    return json;
  }

  /// Returns a new [MealsByDateEntry] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static MealsByDateEntry? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "MealsByDateEntry[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "MealsByDateEntry[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return MealsByDateEntry(
        date: mapDateTime(json, r'date', r'')!,
        meals: PerDateMealItem.listFromJson(json[r'meals']),
      );
    }
    return null;
  }

  static List<MealsByDateEntry> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <MealsByDateEntry>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = MealsByDateEntry.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, MealsByDateEntry> mapFromJson(dynamic json) {
    final map = <String, MealsByDateEntry>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = MealsByDateEntry.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of MealsByDateEntry-objects as value to a dart map
  static Map<String, List<MealsByDateEntry>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<MealsByDateEntry>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = MealsByDateEntry.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'date',
    'meals',
  };
}

