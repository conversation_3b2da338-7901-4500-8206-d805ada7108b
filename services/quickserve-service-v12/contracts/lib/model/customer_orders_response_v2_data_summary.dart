//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class CustomerOrdersResponseV2DataSummary {
  /// Returns a new [CustomerOrdersResponseV2DataSummary] instance.
  CustomerOrdersResponseV2DataSummary({
    this.totalOrders,
    this.upcomingOrders,
    this.cancelledOrders,
    this.otherOrders,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? totalOrders;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? upcomingOrders;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? cancelledOrders;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? otherOrders;

  @override
  bool operator ==(Object other) => identical(this, other) || other is CustomerOrdersResponseV2DataSummary &&
    other.totalOrders == totalOrders &&
    other.upcomingOrders == upcomingOrders &&
    other.cancelledOrders == cancelledOrders &&
    other.otherOrders == otherOrders;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (totalOrders == null ? 0 : totalOrders!.hashCode) +
    (upcomingOrders == null ? 0 : upcomingOrders!.hashCode) +
    (cancelledOrders == null ? 0 : cancelledOrders!.hashCode) +
    (otherOrders == null ? 0 : otherOrders!.hashCode);

  @override
  String toString() => 'CustomerOrdersResponseV2DataSummary[totalOrders=$totalOrders, upcomingOrders=$upcomingOrders, cancelledOrders=$cancelledOrders, otherOrders=$otherOrders]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.totalOrders != null) {
      json[r'total_orders'] = this.totalOrders;
    } else {
      json[r'total_orders'] = null;
    }
    if (this.upcomingOrders != null) {
      json[r'upcoming_orders'] = this.upcomingOrders;
    } else {
      json[r'upcoming_orders'] = null;
    }
    if (this.cancelledOrders != null) {
      json[r'cancelled_orders'] = this.cancelledOrders;
    } else {
      json[r'cancelled_orders'] = null;
    }
    if (this.otherOrders != null) {
      json[r'other_orders'] = this.otherOrders;
    } else {
      json[r'other_orders'] = null;
    }
    return json;
  }

  /// Returns a new [CustomerOrdersResponseV2DataSummary] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static CustomerOrdersResponseV2DataSummary? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "CustomerOrdersResponseV2DataSummary[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "CustomerOrdersResponseV2DataSummary[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return CustomerOrdersResponseV2DataSummary(
        totalOrders: mapValueOfType<int>(json, r'total_orders'),
        upcomingOrders: mapValueOfType<int>(json, r'upcoming_orders'),
        cancelledOrders: mapValueOfType<int>(json, r'cancelled_orders'),
        otherOrders: mapValueOfType<int>(json, r'other_orders'),
      );
    }
    return null;
  }

  static List<CustomerOrdersResponseV2DataSummary> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CustomerOrdersResponseV2DataSummary>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CustomerOrdersResponseV2DataSummary.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, CustomerOrdersResponseV2DataSummary> mapFromJson(dynamic json) {
    final map = <String, CustomerOrdersResponseV2DataSummary>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = CustomerOrdersResponseV2DataSummary.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of CustomerOrdersResponseV2DataSummary-objects as value to a dart map
  static Map<String, List<CustomerOrdersResponseV2DataSummary>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<CustomerOrdersResponseV2DataSummary>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = CustomerOrdersResponseV2DataSummary.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

