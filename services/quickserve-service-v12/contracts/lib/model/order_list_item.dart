//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class OrderListItem {
  /// Returns a new [OrderListItem] instance.
  OrderListItem({
    this.orderId,
    this.orderNo,
    this.orderDate,
    this.orderStatus,
    this.deliveryStatus,
    this.paymentMode,
    this.amountPaid,
    this.totalAmount,
    this.deliveryTime,
    this.deliveryEndTime,
    this.recurringStatus,
    this.daysPreference,
    this.customerAddress,
    this.locationName,
    this.cityName,
    this.foodPreference,
    this.productCode,
    this.productName,
    this.imagePath,
    this.quantity,
    this.itemAmount,
    this.productType,
    this.lastModified,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? orderId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? orderNo;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DateTime? orderDate;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? orderStatus;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? deliveryStatus;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? paymentMode;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? amountPaid;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? totalAmount;

  String? deliveryTime;

  String? deliveryEndTime;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? recurringStatus;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? daysPreference;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? customerAddress;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? locationName;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? cityName;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? foodPreference;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? productCode;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? productName;

  String? imagePath;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? quantity;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? itemAmount;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? productType;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DateTime? lastModified;

  @override
  bool operator ==(Object other) => identical(this, other) || other is OrderListItem &&
    other.orderId == orderId &&
    other.orderNo == orderNo &&
    other.orderDate == orderDate &&
    other.orderStatus == orderStatus &&
    other.deliveryStatus == deliveryStatus &&
    other.paymentMode == paymentMode &&
    other.amountPaid == amountPaid &&
    other.totalAmount == totalAmount &&
    other.deliveryTime == deliveryTime &&
    other.deliveryEndTime == deliveryEndTime &&
    other.recurringStatus == recurringStatus &&
    other.daysPreference == daysPreference &&
    other.customerAddress == customerAddress &&
    other.locationName == locationName &&
    other.cityName == cityName &&
    other.foodPreference == foodPreference &&
    other.productCode == productCode &&
    other.productName == productName &&
    other.imagePath == imagePath &&
    other.quantity == quantity &&
    other.itemAmount == itemAmount &&
    other.productType == productType &&
    other.lastModified == lastModified;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (orderId == null ? 0 : orderId!.hashCode) +
    (orderNo == null ? 0 : orderNo!.hashCode) +
    (orderDate == null ? 0 : orderDate!.hashCode) +
    (orderStatus == null ? 0 : orderStatus!.hashCode) +
    (deliveryStatus == null ? 0 : deliveryStatus!.hashCode) +
    (paymentMode == null ? 0 : paymentMode!.hashCode) +
    (amountPaid == null ? 0 : amountPaid!.hashCode) +
    (totalAmount == null ? 0 : totalAmount!.hashCode) +
    (deliveryTime == null ? 0 : deliveryTime!.hashCode) +
    (deliveryEndTime == null ? 0 : deliveryEndTime!.hashCode) +
    (recurringStatus == null ? 0 : recurringStatus!.hashCode) +
    (daysPreference == null ? 0 : daysPreference!.hashCode) +
    (customerAddress == null ? 0 : customerAddress!.hashCode) +
    (locationName == null ? 0 : locationName!.hashCode) +
    (cityName == null ? 0 : cityName!.hashCode) +
    (foodPreference == null ? 0 : foodPreference!.hashCode) +
    (productCode == null ? 0 : productCode!.hashCode) +
    (productName == null ? 0 : productName!.hashCode) +
    (imagePath == null ? 0 : imagePath!.hashCode) +
    (quantity == null ? 0 : quantity!.hashCode) +
    (itemAmount == null ? 0 : itemAmount!.hashCode) +
    (productType == null ? 0 : productType!.hashCode) +
    (lastModified == null ? 0 : lastModified!.hashCode);

  @override
  String toString() => 'OrderListItem[orderId=$orderId, orderNo=$orderNo, orderDate=$orderDate, orderStatus=$orderStatus, deliveryStatus=$deliveryStatus, paymentMode=$paymentMode, amountPaid=$amountPaid, totalAmount=$totalAmount, deliveryTime=$deliveryTime, deliveryEndTime=$deliveryEndTime, recurringStatus=$recurringStatus, daysPreference=$daysPreference, customerAddress=$customerAddress, locationName=$locationName, cityName=$cityName, foodPreference=$foodPreference, productCode=$productCode, productName=$productName, imagePath=$imagePath, quantity=$quantity, itemAmount=$itemAmount, productType=$productType, lastModified=$lastModified]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.orderId != null) {
      json[r'order_id'] = this.orderId;
    } else {
      json[r'order_id'] = null;
    }
    if (this.orderNo != null) {
      json[r'order_no'] = this.orderNo;
    } else {
      json[r'order_no'] = null;
    }
    if (this.orderDate != null) {
      json[r'order_date'] = _dateFormatter.format(this.orderDate!.toUtc());
    } else {
      json[r'order_date'] = null;
    }
    if (this.orderStatus != null) {
      json[r'order_status'] = this.orderStatus;
    } else {
      json[r'order_status'] = null;
    }
    if (this.deliveryStatus != null) {
      json[r'delivery_status'] = this.deliveryStatus;
    } else {
      json[r'delivery_status'] = null;
    }
    if (this.paymentMode != null) {
      json[r'payment_mode'] = this.paymentMode;
    } else {
      json[r'payment_mode'] = null;
    }
    if (this.amountPaid != null) {
      json[r'amount_paid'] = this.amountPaid;
    } else {
      json[r'amount_paid'] = null;
    }
    if (this.totalAmount != null) {
      json[r'total_amount'] = this.totalAmount;
    } else {
      json[r'total_amount'] = null;
    }
    if (this.deliveryTime != null) {
      json[r'delivery_time'] = this.deliveryTime;
    } else {
      json[r'delivery_time'] = null;
    }
    if (this.deliveryEndTime != null) {
      json[r'delivery_end_time'] = this.deliveryEndTime;
    } else {
      json[r'delivery_end_time'] = null;
    }
    if (this.recurringStatus != null) {
      json[r'recurring_status'] = this.recurringStatus;
    } else {
      json[r'recurring_status'] = null;
    }
    if (this.daysPreference != null) {
      json[r'days_preference'] = this.daysPreference;
    } else {
      json[r'days_preference'] = null;
    }
    if (this.customerAddress != null) {
      json[r'customer_address'] = this.customerAddress;
    } else {
      json[r'customer_address'] = null;
    }
    if (this.locationName != null) {
      json[r'location_name'] = this.locationName;
    } else {
      json[r'location_name'] = null;
    }
    if (this.cityName != null) {
      json[r'city_name'] = this.cityName;
    } else {
      json[r'city_name'] = null;
    }
    if (this.foodPreference != null) {
      json[r'food_preference'] = this.foodPreference;
    } else {
      json[r'food_preference'] = null;
    }
    if (this.productCode != null) {
      json[r'product_code'] = this.productCode;
    } else {
      json[r'product_code'] = null;
    }
    if (this.productName != null) {
      json[r'product_name'] = this.productName;
    } else {
      json[r'product_name'] = null;
    }
    if (this.imagePath != null) {
      json[r'image_path'] = this.imagePath;
    } else {
      json[r'image_path'] = null;
    }
    if (this.quantity != null) {
      json[r'quantity'] = this.quantity;
    } else {
      json[r'quantity'] = null;
    }
    if (this.itemAmount != null) {
      json[r'item_amount'] = this.itemAmount;
    } else {
      json[r'item_amount'] = null;
    }
    if (this.productType != null) {
      json[r'product_type'] = this.productType;
    } else {
      json[r'product_type'] = null;
    }
    if (this.lastModified != null) {
      json[r'last_modified'] = this.lastModified!.toUtc().toIso8601String();
    } else {
      json[r'last_modified'] = null;
    }
    return json;
  }

  /// Returns a new [OrderListItem] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static OrderListItem? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "OrderListItem[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "OrderListItem[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return OrderListItem(
        orderId: mapValueOfType<int>(json, r'order_id'),
        orderNo: mapValueOfType<String>(json, r'order_no'),
        orderDate: mapDateTime(json, r'order_date', r''),
        orderStatus: mapValueOfType<String>(json, r'order_status'),
        deliveryStatus: mapValueOfType<String>(json, r'delivery_status'),
        paymentMode: mapValueOfType<String>(json, r'payment_mode'),
        amountPaid: num.parse('${json[r'amount_paid']}'),
        totalAmount: num.parse('${json[r'total_amount']}'),
        deliveryTime: mapValueOfType<String>(json, r'delivery_time'),
        deliveryEndTime: mapValueOfType<String>(json, r'delivery_end_time'),
        recurringStatus: mapValueOfType<int>(json, r'recurring_status'),
        daysPreference: mapValueOfType<String>(json, r'days_preference'),
        customerAddress: mapValueOfType<String>(json, r'customer_address'),
        locationName: mapValueOfType<String>(json, r'location_name'),
        cityName: mapValueOfType<String>(json, r'city_name'),
        foodPreference: mapValueOfType<String>(json, r'food_preference'),
        productCode: mapValueOfType<int>(json, r'product_code'),
        productName: mapValueOfType<String>(json, r'product_name'),
        imagePath: mapValueOfType<String>(json, r'image_path'),
        quantity: mapValueOfType<int>(json, r'quantity'),
        itemAmount: num.parse('${json[r'item_amount']}'),
        productType: mapValueOfType<String>(json, r'product_type'),
        lastModified: mapDateTime(json, r'last_modified', r''),
      );
    }
    return null;
  }

  static List<OrderListItem> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <OrderListItem>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = OrderListItem.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, OrderListItem> mapFromJson(dynamic json) {
    final map = <String, OrderListItem>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = OrderListItem.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of OrderListItem-objects as value to a dart map
  static Map<String, List<OrderListItem>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<OrderListItem>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = OrderListItem.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

