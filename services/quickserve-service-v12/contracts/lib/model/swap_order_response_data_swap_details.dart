//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SwapOrderResponseDataSwapDetails {
  /// Returns a new [SwapOrderResponseDataSwapDetails] instance.
  SwapOrderResponseDataSwapDetails({
    this.oldProduct,
    this.newProduct,
    this.priceDifference,
    this.swapCharges,
    this.totalAmountChange,
    this.newOrderAmount,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  SwapOrderResponseDataSwapDetailsOldProduct? oldProduct;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  SwapOrderResponseDataSwapDetailsNewProduct? newProduct;

  /// Price difference between old and new product
  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? priceDifference;

  /// Additional charges for the swap
  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? swapCharges;

  /// Total amount change (price difference + swap charges)
  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? totalAmountChange;

  /// New total order amount after swap
  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? newOrderAmount;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SwapOrderResponseDataSwapDetails &&
    other.oldProduct == oldProduct &&
    other.newProduct == newProduct &&
    other.priceDifference == priceDifference &&
    other.swapCharges == swapCharges &&
    other.totalAmountChange == totalAmountChange &&
    other.newOrderAmount == newOrderAmount;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (oldProduct == null ? 0 : oldProduct!.hashCode) +
    (newProduct == null ? 0 : newProduct!.hashCode) +
    (priceDifference == null ? 0 : priceDifference!.hashCode) +
    (swapCharges == null ? 0 : swapCharges!.hashCode) +
    (totalAmountChange == null ? 0 : totalAmountChange!.hashCode) +
    (newOrderAmount == null ? 0 : newOrderAmount!.hashCode);

  @override
  String toString() => 'SwapOrderResponseDataSwapDetails[oldProduct=$oldProduct, newProduct=$newProduct, priceDifference=$priceDifference, swapCharges=$swapCharges, totalAmountChange=$totalAmountChange, newOrderAmount=$newOrderAmount]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.oldProduct != null) {
      json[r'old_product'] = this.oldProduct;
    } else {
      json[r'old_product'] = null;
    }
    if (this.newProduct != null) {
      json[r'new_product'] = this.newProduct;
    } else {
      json[r'new_product'] = null;
    }
    if (this.priceDifference != null) {
      json[r'price_difference'] = this.priceDifference;
    } else {
      json[r'price_difference'] = null;
    }
    if (this.swapCharges != null) {
      json[r'swap_charges'] = this.swapCharges;
    } else {
      json[r'swap_charges'] = null;
    }
    if (this.totalAmountChange != null) {
      json[r'total_amount_change'] = this.totalAmountChange;
    } else {
      json[r'total_amount_change'] = null;
    }
    if (this.newOrderAmount != null) {
      json[r'new_order_amount'] = this.newOrderAmount;
    } else {
      json[r'new_order_amount'] = null;
    }
    return json;
  }

  /// Returns a new [SwapOrderResponseDataSwapDetails] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SwapOrderResponseDataSwapDetails? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SwapOrderResponseDataSwapDetails[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SwapOrderResponseDataSwapDetails[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SwapOrderResponseDataSwapDetails(
        oldProduct: SwapOrderResponseDataSwapDetailsOldProduct.fromJson(json[r'old_product']),
        newProduct: SwapOrderResponseDataSwapDetailsNewProduct.fromJson(json[r'new_product']),
        priceDifference: num.parse('${json[r'price_difference']}'),
        swapCharges: num.parse('${json[r'swap_charges']}'),
        totalAmountChange: num.parse('${json[r'total_amount_change']}'),
        newOrderAmount: num.parse('${json[r'new_order_amount']}'),
      );
    }
    return null;
  }

  static List<SwapOrderResponseDataSwapDetails> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SwapOrderResponseDataSwapDetails>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SwapOrderResponseDataSwapDetails.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SwapOrderResponseDataSwapDetails> mapFromJson(dynamic json) {
    final map = <String, SwapOrderResponseDataSwapDetails>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SwapOrderResponseDataSwapDetails.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SwapOrderResponseDataSwapDetails-objects as value to a dart map
  static Map<String, List<SwapOrderResponseDataSwapDetails>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SwapOrderResponseDataSwapDetails>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SwapOrderResponseDataSwapDetails.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

