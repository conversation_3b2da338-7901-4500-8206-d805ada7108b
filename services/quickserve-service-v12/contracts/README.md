# openapi
Comprehensive order management system for QuickServe food delivery platform with advanced cancellation policies and order swap functionality.

## Complete Order Journey
1. **Create Pre-Order** - Creates temp_pre_orders, temp_order_payment, payment_transaction (initiated)
2. **Payment Processing** - Mobile app uses Payment Service v12 APIs
3. **Payment Success** - Updates payment_transaction, creates payment_transfered (Razorpay), updates temp_order_payment
4. **Order Creation** - Creates multiple orders (15 days = 12 weekday orders) and order_details (3 items × 12 orders = 36 records)
5. **Wallet Locking** - Locks wallet amount for each order for cancellation tracking
6. **Order Fulfillment** - All orders ready for delivery with status \"New\" (not \"Confirmed\")

## Advanced Cancellation System
### Time-Based Refund Policies:
1. **Before Cutoff Time** → 100% refund + unlock wallet amount
2. **Partial Refund Window (00:01:00-08:00:00)** → Breakfast: 0%, Lunch: 50% + unlock wallet
3. **After 08:00:00** → No cancellation allowed

## Order Swap System
- Allows customers to swap meals within the same category
- Automatic price difference calculation and tax recalculation
- Comprehensive validation and audit logging


This Dart package is automatically generated by the [OpenAPI Generator](https://openapi-generator.tech) project:

- API version: 2.1.0
- Generator version: 7.15.0
- Build package: org.openapitools.codegen.languages.DartClientCodegen

## Requirements

Dart 2.12 or later

## Installation & Usage

### Github
If this Dart package is published to Github, add the following dependency to your pubspec.yaml
```
dependencies:
  openapi:
    git: https://github.com/GIT_USER_ID/GIT_REPO_ID.git
```

### Local
To use the package in your local drive, add the following dependency to your pubspec.yaml
```
dependencies:
  openapi:
    path: /path/to/openapi
```

## Tests

TODO

## Getting Started

Please follow the [installation procedure](#installation--usage) and then run the following:

```dart
import 'package:openapi/api.dart';

// TODO Configure HTTP Bearer authorization: BearerAuth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('BearerAuth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('BearerAuth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = OrderManagementApi();
final applyCouponRequest = ApplyCouponRequest(); // ApplyCouponRequest | 

try {
    final result = api_instance.orderManagementApplyCouponPost(applyCouponRequest);
    print(result);
} catch (e) {
    print('Exception when calling OrderManagementApi->orderManagementApplyCouponPost: $e\n');
}

```

## Documentation for API Endpoints

All URIs are relative to *http://************:8000/api/v2*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
*OrderManagementApi* | [**orderManagementApplyCouponPost**](doc//OrderManagementApi.md#ordermanagementapplycouponpost) | **POST** /order-management/apply-coupon | Apply coupon to pre-order or order
*OrderManagementApi* | [**orderManagementCancelOrderNoPost**](doc//OrderManagementApi.md#ordermanagementcancelordernopost) | **POST** /order-management/cancel/{orderNo} | Cancel order with time-based refund processing
*OrderManagementApi* | [**orderManagementCreatePost**](doc//OrderManagementApi.md#ordermanagementcreatepost) | **POST** /order-management/create | Create new order with payment integration (Normal and Express)
*OrderManagementApi* | [**orderManagementCustomerCustomerCodeGet**](doc//OrderManagementApi.md#ordermanagementcustomercustomercodeget) | **GET** /order-management/customer/{customerCode} | Get customer orders with cancellation details
*OrderManagementApi* | [**orderManagementDetailsOrderNoGet**](doc//OrderManagementApi.md#ordermanagementdetailsordernoget) | **GET** /order-management/details/{orderNo} | Get order details with payment status
*OrderManagementApi* | [**orderManagementPaymentFailureOrderNoPost**](doc//OrderManagementApi.md#ordermanagementpaymentfailureordernopost) | **POST** /order-management/payment-failure/{orderNo} | Payment failure callback from payment service
*OrderManagementApi* | [**orderManagementPaymentSuccessOrderNoPost**](doc//OrderManagementApi.md#ordermanagementpaymentsuccessordernopost) | **POST** /order-management/payment-success/{orderNo} | Payment success callback from payment service
*OrderManagementApi* | [**orderManagementPreOrderStatusOrderNoGet**](doc//OrderManagementApi.md#ordermanagementpreorderstatusordernoget) | **GET** /order-management/pre-order-status/{orderNo} | Check pre-order status for a given order number
*OrderManagementApi* | [**orderManagementSwapOrderNoPost**](doc//OrderManagementApi.md#ordermanagementswapordernopost) | **POST** /order-management/swap/{orderNo} | Swap order product with another product from the same category


## Documentation For Models

 - [ApplyCouponRequest](doc//ApplyCouponRequest.md)
 - [CancelOrderRequest](doc//CancelOrderRequest.md)
 - [CancelOrderResponse](doc//CancelOrderResponse.md)
 - [CancelOrderResponseData](doc//CancelOrderResponseData.md)
 - [CancelOrderResponseDataCancellationDetails](doc//CancelOrderResponseDataCancellationDetails.md)
 - [CreateOrderBase](doc//CreateOrderBase.md)
 - [CreateOrderRequest](doc//CreateOrderRequest.md)
 - [CreateOrderResponse](doc//CreateOrderResponse.md)
 - [CreateOrderResponseData](doc//CreateOrderResponseData.md)
 - [CreateOrderResponseDataExpress](doc//CreateOrderResponseDataExpress.md)
 - [CustomerOrdersResponse](doc//CustomerOrdersResponse.md)
 - [CustomerOrdersResponseV2](doc//CustomerOrdersResponseV2.md)
 - [CustomerOrdersResponseV2Data](doc//CustomerOrdersResponseV2Data.md)
 - [CustomerOrdersResponseV2DataCustomer](doc//CustomerOrdersResponseV2DataCustomer.md)
 - [CustomerOrdersResponseV2DataFilters](doc//CustomerOrdersResponseV2DataFilters.md)
 - [CustomerOrdersResponseV2DataOrders](doc//CustomerOrdersResponseV2DataOrders.md)
 - [CustomerOrdersResponseV2DataPagination](doc//CustomerOrdersResponseV2DataPagination.md)
 - [CustomerOrdersResponseV2DataSummary](doc//CustomerOrdersResponseV2DataSummary.md)
 - [ErrorResponse](doc//ErrorResponse.md)
 - [ExpressCreateOrderRequest](doc//ExpressCreateOrderRequest.md)
 - [MealCartItem](doc//MealCartItem.md)
 - [MealCartItemShort](doc//MealCartItemShort.md)
 - [MealsByDateEntry](doc//MealsByDateEntry.md)
 - [NormalCreateOrderRequest](doc//NormalCreateOrderRequest.md)
 - [NormalPerDateModel](doc//NormalPerDateModel.md)
 - [NormalRepeatingModel](doc//NormalRepeatingModel.md)
 - [OrderListItem](doc//OrderListItem.md)
 - [OrderManagementApplyCouponPost200Response](doc//OrderManagementApplyCouponPost200Response.md)
 - [OrderManagementCreatePostRequest](doc//OrderManagementCreatePostRequest.md)
 - [OrderManagementPreOrderStatusOrderNoGet200Response](doc//OrderManagementPreOrderStatusOrderNoGet200Response.md)
 - [PaginationMeta](doc//PaginationMeta.md)
 - [PaymentCallbackRequest](doc//PaymentCallbackRequest.md)
 - [PerDateMealItem](doc//PerDateMealItem.md)
 - [SwapOrderRequest](doc//SwapOrderRequest.md)
 - [SwapOrderResponse](doc//SwapOrderResponse.md)
 - [SwapOrderResponseData](doc//SwapOrderResponseData.md)
 - [SwapOrderResponseDataSwapDetails](doc//SwapOrderResponseDataSwapDetails.md)
 - [SwapOrderResponseDataSwapDetailsNewProduct](doc//SwapOrderResponseDataSwapDetailsNewProduct.md)
 - [SwapOrderResponseDataSwapDetailsOldProduct](doc//SwapOrderResponseDataSwapDetailsOldProduct.md)
 - [ValidationErrorResponse](doc//ValidationErrorResponse.md)


## Documentation For Authorization


Authentication schemes defined for the API:
### BearerAuth

- **Type**: HTTP Bearer authentication


## Author

<EMAIL>

