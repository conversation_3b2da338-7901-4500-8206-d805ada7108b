openapi: 3.0.3
info:
  title: OneFoodDialer 2025 - Customer Service API
  description: |
    Customer service for OneFoodDialer 2025 platform - JWT Protected Customer APIs.

    ## Features
    - Customer address management with student profile support
    - Product menu retrieval based on kitchen menu types and categories
    - Delivery location management for schools and locations
    - JWT authentication for secure access

    ## Authentication
    All endpoints require JWT authentication via Bearer token using jwt.auth middleware.

    ## Student Profile Format
    For customer addresses with menu_type='school', student details are stored in location_address field:
    "Child Name, Class, Division, Floor, Allergies"
    Where allergies are separated by " - " (e.g., "Wheat - Lactose - Nuts")

    ## Database Configuration
    - Database: live_quickserve_8163
    - Company ID: 8163 (configured via environment)
    - All operations are automatically filtered by company_id
  version: 2.0.1
  contact:
    name: OneFoodDialer 2025 API Support
    email: <EMAIL>
    url: https://docs.onefooddialer.com
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://*************:8000
    description: Proxy server
  - url: http://**************:9001/api
    description: Production server
  - url: http://************:8000/api
    description: Local server

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    CustomerAddressListResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Customer addresses retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/CustomerAddress'
        meta:
          type: object
          properties:
            current_page:
              type: integer
              example: 1
            last_page:
              type: integer
              example: 5
            per_page:
              type: integer
              example: 15
            total:
              type: integer
              example: 67
            customer_id:
              type: integer
              example: 3787
            company_id:
              type: integer
              example: 8163
            timestamp:
              type: string
              format: date-time
              example: "2025-01-08T10:30:00.000000Z"
            api_version:
              type: string
              example: "v2"

    CustomerAddressResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Customer address retrieved successfully"
        data:
          $ref: '#/components/schemas/CustomerAddress'
        meta:
          type: object
          properties:
            customer_id:
              type: integer
              example: 3787
            company_id:
              type: integer
              example: 8163
            timestamp:
              type: string
              format: date-time
              example: "2025-01-08T10:30:00.000000Z"
            api_version:
              type: string
              example: "v2"

    CustomerAddress:
      type: object
      properties:
        pk_customer_address_code:
          type: integer
          example: 123
        fk_customer_code:
          type: integer
          example: 3787
        location_address:
          type: string
          example: "John Doe, 5th Grade, A, Ground Floor, Wheat - Lactose"
        location_code:
          type: integer
          example: 101
        menu_type:
          type: string
          example: "school"
        city:
          type: string
          example: "Mumbai"
        is_default:
          type: integer
          example: 1
        status:
          type: integer
          example: 1
        created_at:
          type: string
          format: date-time
          example: "2025-01-01T00:00:00.000000Z"
        updated_at:
          type: string
          format: date-time
          example: "2025-01-08T10:30:00.000000Z"
        student_profile:
          type: object
          nullable: true
          properties:
            child_name:
              type: string
              example: "John Doe"
            class:
              type: string
              example: "5th Grade"
            division:
              type: string
              example: "A"
            floor:
              type: string
              example: "Ground Floor"
            allergies:
              type: array
              items:
                type: string
              example: ["Wheat", "Lactose"]

    ProductMenuResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Product menu retrieved successfully"
        data:
          type: object
          properties:
            delivery_location:
              $ref: '#/components/schemas/DeliveryLocation'
            kitchen:
              $ref: '#/components/schemas/Kitchen'
            menu_type:
              type: string
              example: "breakfast,lunch"
            product_categories:
              type: array
              items:
                $ref: '#/components/schemas/ProductCategory'
            products_by_category:
              type: object
              properties:
                breakfast:
                  type: array
                  items:
                    $ref: '#/components/schemas/Product'
                lunch:
                  type: array
                  items:
                    $ref: '#/components/schemas/Product'
            meal_types:
              type: array
              description: List of meal types with their products; returned when date range filters are applied or by implementation choice.
              items:
                $ref: '#/components/schemas/MealTypeProducts'
            summary:
              type: object
              description: Summary of counts for the current response payload.
              properties:
                total_meal_types:
                  type: integer
                  example: 2
                total_products:
                  type: integer
                  example: 8
        meta:
          type: object
          properties:
            total_products:
              type: integer
              example: 25
            total_categories:
              type: integer
              example: 2
            from_date:
              type: string
              format: date
              nullable: true
              description: Start of requested date range when provided
              example: "2025-08-25"
            to_date:
              type: string
              format: date
              nullable: true
              description: End of requested date range when provided
              example: "2025-08-29"
            timestamp:
              type: string
              format: date-time
              example: "2025-01-08T10:30:00.000000Z"

    MealTypeProducts:
      type: object
      properties:
        meal_type:
          type: string
          example: "breakfast"
        meal_type_display:
          type: string
          example: "Short break"
        menu_display_name:
          type: string
          example: "Short break"
        product_count:
          type: integer
          example: 4
        products:
          type: array
          items:
            $ref: '#/components/schemas/ProductWithMenu'

    ProductWithMenu:
      allOf:
        - $ref: '#/components/schemas/Product'
        - type: object
          description: |
            Extends Product with menu details. When from_date/to_date are provided, menu_items_by_date contains a map of date to items and menu_items may reflect a general description. When only date is provided (or omitted), menu_items reflect items for that day and menu_items_by_date is null.
          properties:
            menu_items:
              type: string
              nullable: true
              example: "Breakfast Item 1, Breakfast Item 2, Seasonal Fruits"
            menu_items_by_date:
              type: object
              nullable: true
              additionalProperties:
                type: string
              example:
                "2025-08-18": "Rice Flat Noodles, Seasonal Fruits"
                "2025-08-19": "Grilled Veg Galouti Kebab, Coriander Mint Chutney, Seasonal Fruits"

    DeliveryLocationListResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Delivery locations retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/DeliveryLocation'
        meta:
          type: object
          properties:
            current_page:
              type: integer
              example: 1
            last_page:
              type: integer
              example: 3
            per_page:
              type: integer
              example: 15
            total:
              type: integer
              example: 42
            company_id:
              type: integer
              example: 8163
            timestamp:
              type: string
              format: date-time
              example: "2025-01-08T10:30:00.000000Z"

    DeliveryLocationResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Delivery location retrieved successfully"
        data:
          $ref: '#/components/schemas/DeliveryLocation'
        meta:
          type: object
          properties:
            company_id:
              type: integer
              example: 8163
            timestamp:
              type: string
              format: date-time
              example: "2025-01-08T10:30:00.000000Z"

    DeliveryLocation:
      type: object
      properties:
        pk_location_code:
          type: integer
          example: 101
        location:
          type: string
          example: "ABC International School"
        city:
          type: string
          example: "Mumbai"
        status:
          type: integer
          example: 1
        fk_company_code:
          type: integer
          example: 8163
        created_at:
          type: string
          format: date-time
          example: "2025-01-01T00:00:00.000000Z"
        updated_at:
          type: string
          format: date-time
          example: "2025-01-08T10:30:00.000000Z"

    Kitchen:
      type: object
      properties:
        pk_kitchen_code:
          type: integer
          example: 1
        kitchen_alias:
          type: string
          example: "main_kitchen"
        kitchen_name:
          type: string
          example: "Main Kitchen"
        status:
          type: integer
          example: 1

    ProductCategory:
      type: object
      properties:
        pk_product_category_code:
          type: integer
          example: 1
        category_name:
          type: string
          example: "breakfast"
        category_type:
          type: string
          example: "meal"
        status:
          type: integer
          example: 1

    Product:
      type: object
      properties:
        pk_product_code:
          type: integer
          example: 123
        name:
          type: string
          example: "Breakfast Combo"
        unit_price:
          type: number
          format: float
          example: 150.00
        category:
          type: string
          example: "breakfast"
        food_type:
          type: string
          example: "veg"
        status:
          type: integer
          example: 1

    WalletDetails:
      type: object
      properties:
        customer_id:
          type: integer
          example: 1543
        customer_code:
          type: integer
          example: 1543
        balance:
          type: number
          format: float
          description: "Legacy balance field (same as available_balance)"
          example: 367.5
        available_balance:
          type: number
          format: float
          description: "Amount customer can actually spend (credits - debits - locked - pending orders)"
          example: 367.5
        locked_balance:
          type: number
          format: float
          description: "Amount locked in wallet transactions"
          example: 0
        total_credit:
          type: number
          format: float
          description: "Total amount credited to wallet"
          example: 16511.25
        total_debit:
          type: number
          format: float
          description: "Total amount debited from wallet"
          example: 16143.75
        pending_order_amount:
          type: number
          format: float
          description: "Amount locked due to pending orders (New, Confirmed, Processing)"
          example: 150.0
        delivered_order_amount:
          type: number
          format: float
          description: "Amount deducted due to delivered orders (Complete, Delivered)"
          example: 0.0
        effective_locked:
          type: number
          format: float
          description: "Total effective locked amount (locked_balance + pending_order_amount)"
          example: 150.0
        effective_debit:
          type: number
          format: float
          description: "Total effective debit amount (total_debit + delivered_order_amount)"
          example: 16143.75
        currency:
          type: string
          example: INR
        status:
          type: string
          example: active
        calculation_method:
          type: string
          description: "Method used for balance calculation"
          example: "order_status_based"
          enum: ["order_status_based", "wallet_entries_only"]

    WalletTransaction:
      type: object
      properties:
        id:
          type: integer
          example: 217555
        customer_code:
          type: integer
          example: 1543
        amount:
          type: number
          format: float
          example: 78.75
        type:
          type: string
          enum: [credit, debit, lock]
          example: debit
        description:
          type: string
          example: "Rs. 78.75 deducted against Bill No. 126413 of Order No. 3WAA250629"
        transaction_id:
          type: string
          example: "OMO2506291050393731401561"
        date:
          type: string
          format: date
          example: "2025-07-02"
        payment_type:
          type: string
          enum: [neft, cash, cheque, online, wallet, partial]
          example: wallet
        context:
          type: string
          enum: [admin, customer]
          example: admin
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    WalletTransactionsResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            meta:
              type: object
              properties:
                customer_id:
                  type: integer
                  example: 1543
                total_balance:
                  type: number
                  example: 367.5
                available_balance:
                  type: number
                  example: 367.5
                usable_balance:
                  type: number
                  example: 367.5
                locked_balance:
                  type: number
                  example: 0
                currency:
                  type: string
                  example: INR
            history:
              type: object
              properties:
                data:
                  type: array
                  items:
                    $ref: '#/components/schemas/WalletTransaction'
                pagination:
                  type: object
                  properties:
                    current_page:
                      type: integer
                      example: 1
                    per_page:
                      type: integer
                      example: 15
                    total:
                      type: integer
                      example: 50
                    last_page:
                      type: integer
                      example: 4

    WalletWithHistoryResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            wallet_details:
              $ref: '#/components/schemas/WalletDetails'
            wallet_history:
              type: object
              properties:
                data:
                  type: array
                  items:
                    $ref: '#/components/schemas/WalletTransaction'
                pagination:
                  type: object
                  properties:
                    current_page:
                      type: integer
                      example: 1
                    per_page:
                      type: integer
                      example: 15
                    total:
                      type: integer
                      example: 50
                    last_page:
                      type: integer
                      example: 4

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Error message"
        errors:
          type: object
          additionalProperties: true
          example:
            customer_id: ["The customer id field is required."]

security:
  - bearerAuth: []

tags:
  - name: Customer Address
    description: Customer address management with student profile support
  - name: Product Menu
    description: Product menu retrieval based on kitchen menu types
  - name: Wallet Management
    description: Customer wallet balance and transaction operations
  - name: Delivery Locations
    description: Delivery location management for schools and locations

paths:
  /v2/customers/find-or-create-keycloak:
    post:
      summary: Find or create customer from Keycloak authentication
      description: |
        **Merged API that combines customer lookup and registration to reduce API calls.**

        This endpoint implements a find-or-create pattern:
        1. **Search First**: Looks for existing customer by phone or email (username field)
        2. **If Found**: Returns existing customer data with `"action": "found"`
        3. **If Not Found**: Creates new customer and returns data with `"action": "created"`

        **Benefits:**
        - Reduces API calls from 2 to 1 (no need for separate lookup + registration)
        - Atomic operation with single transaction
        - Handles both new and existing customers seamlessly
        - Updates auth_id for legacy customers without Keycloak integration

        **Authentication:**
        - Requires JWT token via Bearer authentication
        - Protected by jwt.auth middleware

        **Use Case:**
        Perfect for post-authentication customer identification where you need to ensure
        a customer record exists after successful login through auth service.
      operationId: findOrCreateCustomerFromKeycloak
      tags: [Customer Management]
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - username
                - auth_id
                - customer_name
                - phone
                - company_id
              properties:
                username:
                  type: string
                  maxLength: 255
                  description: Phone number or email for customer lookup
                  example: "************"
                auth_id:
                  type: string
                  maxLength: 255
                  description: Keycloak user UUID from auth service
                  example: "keycloak-uuid-12345"
                customer_name:
                  type: string
                  maxLength: 45
                  description: Full name of the customer
                  example: "John Doe"
                email_address:
                  type: string
                  format: email
                  maxLength: 45
                  nullable: true
                  description: Customer email address
                  example: "<EMAIL>"
                phone:
                  type: string
                  maxLength: 15
                  description: Customer phone number
                  example: "************"
                company_id:
                  type: integer
                  description: Company ID for the customer
                  example: 8163
                unit_id:
                  type: integer
                  nullable: true
                  description: Unit ID (defaults to company_id if not provided)
                  example: 8163
                customer_address:
                  type: string
                  maxLength: 200
                  nullable: true
                  description: Customer address
                  example: "123 Main Street, City"
                food_preference:
                  type: string
                  maxLength: 100
                  nullable: true
                  description: Food preference
                  example: "veg"
                  enum: ["veg", "non-veg", "both"]
                registered_from:
                  type: string
                  maxLength: 50
                  nullable: true
                  description: Registration source
                  example: "keycloak_auth"
                source:
                  type: string
                  maxLength: 50
                  nullable: true
                  description: Source system
                  example: "auth_service"
                thirdparty:
                  type: string
                  maxLength: 255
                  nullable: true
                  description: Third-party identifier (optional)
            examples:
              new_customer:
                summary: New customer creation
                description: Example for creating a new customer
                value:
                  username: "************"
                  auth_id: "keycloak-uuid-12345"
                  customer_name: "John Doe"
                  email_address: "<EMAIL>"
                  phone: "************"
                  company_id: 8163
                  unit_id: 8163
                  customer_address: "123 Main Street"
                  food_preference: "veg"
                  registered_from: "keycloak_auth"
                  source: "auth_service"
              existing_customer:
                summary: Existing customer lookup
                description: Example for finding an existing customer
                value:
                  username: "************"
                  auth_id: "keycloak-uuid-67890"
                  customer_name: "Jane Smith"
                  email_address: "<EMAIL>"
                  phone: "************"
                  company_id: 8163
      responses:
        '200':
          description: Existing customer found
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Customer found"
                  action:
                    type: string
                    enum: ["found"]
                    example: "found"
                  data:
                    type: object
                    properties:
                      pk_customer_code:
                        type: integer
                        description: Customer ID (primary key)
                        example: 12345
                      auth_id:
                        type: integer
                        description: Authentication ID linked to customer
                        example: 12345
                      customer_name:
                        type: string
                        example: "John Doe"
                      email_address:
                        type: string
                        example: "<EMAIL>"
                      phone:
                        type: string
                        example: "************"
                      company_id:
                        type: integer
                        example: 8163
                      unit_id:
                        type: integer
                        example: 8163
        '201':
          description: New customer created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Customer created successfully"
                  action:
                    type: string
                    enum: ["created"]
                    example: "created"
                  data:
                    type: object
                    properties:
                      pk_customer_code:
                        type: integer
                        description: Customer ID (primary key)
                        example: 12346
                      auth_id:
                        type: integer
                        description: Authentication ID linked to customer
                        example: 12346
                      customer_name:
                        type: string
                        example: "John Doe"
                      email_address:
                        type: string
                        example: "<EMAIL>"
                      phone:
                        type: string
                        example: "************"
                      company_id:
                        type: integer
                        example: 8163
                      unit_id:
                        type: integer
                        example: 8163
                      keycloak_user_id:
                        type: string
                        description: Original Keycloak UUID
                        example: "keycloak-uuid-12345"
                      keycloak_hash:
                        type: integer
                        description: Hash of Keycloak UUID stored in thirdparty field
                        example: *********
        '401':
          description: Unauthorized - Invalid or missing JWT token
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Unauthorized"
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Validation failed"
                  errors:
                    type: object
                    additionalProperties:
                      type: array
                      items:
                        type: string
                    example:
                      username: ["The username field is required."]
                      auth_id: ["The auth id field is required."]
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Customer lookup/creation failed: Database connection error"

  /v2/customer-address:
    get:
      summary: Get customer addresses
      description: |
        Retrieve a paginated list of customer addresses for a specific customer.
        Supports filtering by menu type, location, and search functionality.

        **Student Profile Support:**
        - For addresses with menu_type='school', student profile is automatically parsed
        - Student details are extracted from location_address field
        - Format: "Child Name, Class, Division, Floor, Allergies"
      operationId: getCustomerAddresses
      tags: [Customer Address]
      parameters:
        - name: company_id
          in: query
          required: true
          schema:
            type: integer
            minimum: 1
            maximum: 99999
          description: Company identifier to scope data
          example: 8163
        - name: customer_id
          in: query
          required: true
          schema:
            type: integer
          description: Customer ID for filtering addresses
          example: 3787
        - name: per_page
          in: query
          required: false
          schema:
            type: integer
            default: 15
            minimum: 1
            maximum: 100
          description: Number of items per page
          example: 15
        - name: menu_type
          in: query
          required: false
          schema:
            type: string
          description: Filter by menu type (e.g., school, home)
        - name: location_code
          in: query
          required: false
          schema:
            type: integer
          description: Filter by location code
        - name: search
          in: query
          required: false
          schema:
            type: string
          description: Search in location address
        - name: city
          in: query
          required: false
          schema:
            type: string
          description: Filter by city
          example: 9
        - name: show_all
          in: query
          required: false
          schema:
            type: string
            enum: ["true", "false"]
          description: Show all addresses (admin access required)
      responses:
        '200':
          description: Customer addresses retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerAddressListResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    post:
      summary: Create customer address
      description: Create a new customer address (student profile for school menu).
      operationId: createCustomerAddress
      tags: [Customer Address]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [customer_id, company_id, location_code, child_name, class, division, floor]
              properties:
                customer_id:
                  type: integer
                  example: 3787
                company_id:
                  type: integer
                  minimum: 1
                  maximum: 99999
                  example: 8163
                location_code:
                  type: integer
                  example: 2
                child_name:
                  type: string
                  maxLength: 100
                class:
                  type: string
                  maxLength: 10
                division:
                  type: string
                  maxLength: 10
                floor:
                  type: string
                  maxLength: 50
                allergies:
                  type: array
                  items:
                    type: string
                    maxLength: 50
                menu_type:
                  type: string
                  enum: [school]
                city:
                  type: string
                default:
                  type: boolean
      responses:
        '201':
          description: Customer address created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerAddressResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/customer-address/{id}:
    get:
      summary: Get specific customer address
      description: |
        Retrieve detailed information about a specific customer address.
        Includes student profile parsing for school addresses.
      operationId: getCustomerAddressById
      tags: [Customer Address]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Customer address ID
          example: 27233
        - name: company_id
          in: query
          required: true
          schema:
            type: integer
            minimum: 1
            maximum: 99999
          description: Company identifier to scope data
          example: 8163
        - name: customer_id
          in: query
          required: true
          schema:
            type: integer
          description: Customer ID for authorization
          example: 3787
      responses:
        '200':
          description: Customer address retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerAddressResponse'
        '404':
          description: Customer address not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Customer address not found"
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    put:
      summary: Update customer address
      description: Update a customer address by ID.
      operationId: updateCustomerAddress
      tags: [Customer Address]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Customer address ID
          example: 27233
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [customer_id, company_id]
              properties:
                customer_id:
                  type: integer
                  example: 3787
                company_id:
                  type: integer
                  minimum: 1
                  maximum: 99999
                  example: 8163
                location_code:
                  type: integer
                child_name:
                  type: string
                  maxLength: 100
                class:
                  type: string
                  maxLength: 10
                division:
                  type: string
                  maxLength: 10
                floor:
                  type: string
                  maxLength: 50
                allergies:
                  type: array
                  items:
                    type: string
                    maxLength: 50
                default:
                  type: boolean
      responses:
        '200':
          description: Customer address updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerAddressResponse'
        '404':
          description: Customer address not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    delete:
      summary: Delete customer address
      description: Delete a customer address by ID.
      operationId: deleteCustomerAddress
      tags: [Customer Address]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Customer address ID
          example: 27233
        - name: customer_id
          in: query
          required: true
          schema:
            type: integer
          description: Customer ID for authorization
          example: 3787
        - name: company_id
          in: query
          required: true
          schema:
            type: integer
            minimum: 1
            maximum: 99999
          description: Company identifier to scope data
          example: 8163
      responses:
        '200':
          description: Customer address deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Customer address deleted successfully
                  meta:
                    type: object
        '404':
          description: Customer address not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/customer-address/{id}/set-default:
    patch:
      summary: Set default customer address
      description: Set the specified address as default for the customer, scoped by company.
      operationId: setDefaultCustomerAddress
      tags: [Customer Address]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Customer address ID to set as default
          example: 27233
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [customer_id, company_id]
              properties:
                customer_id:
                  type: integer
                  example: 3787
                company_id:
                  type: integer
                  minimum: 1
                  maximum: 99999
                  example: 8163
      responses:
        '200':
          description: Default address updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerAddressResponse'
        '404':
          description: Customer address not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/customer-address/selected:
    get:
      summary: Get selected customer address
      description: Returns the currently selected (default) address for the customer, scoped by company.
      operationId: getSelectedCustomerAddress
      tags: [Customer Address]
      parameters:
        - name: customer_id
          in: query
          required: true
          schema:
            type: integer
          description: Customer ID
          example: 3787
        - name: company_id
          in: query
          required: true
          schema:
            type: integer
            minimum: 1
            maximum: 99999
          description: Company identifier to scope data
          example: 8163
      responses:
        '200':
          description: Selected address fetched successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerAddressResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/product-menu:
    get:
      summary: Get product menu
      description: |
        Retrieve active products/meals based on kitchen's menu type and organized by categories.

        **Logic Flow:**
        1. Determine kitchen and its active menu type (using menu management fallback logic)
        2. Get active product categories of type "meal"
        3. Filter active products that match both the kitchen's menu type and product categories
        4. Bifurcate products by category name for organized display
        5. Sort products with intelligent prioritization

        **Menu Type Filtering:**
        - If kitchen has menu type "breakfast,lunch", only products with category containing "breakfast" or "lunch" are shown
        - Uses fallback logic: kitchen-specific → global → null

        **Product Sorting:**
        - Products with "Recommended" in their name are prioritized and shown first
        - Within each group (recommended/non-recommended), products are sorted by product code (pk_product_code) in ascending order
        - Case-insensitive matching for "Recommended" keyword
        
        **Express Window & Charges (feature-gated):**
        - Applies when the company enables express behavior via settings; the service will resolve
          express window and charge settings per-company using the `company_id` query parameter.
        - When active, menu disable/enable and express charges consider an "express window" that can extend availability past the normal cutoff.
        - Express window configuration keys are resolved hierarchically (first match wins):
          1) `K{kitchenCode}_{MEAL}_EXPRESS_WINDOW_START`, `K{kitchenCode}_{MEAL}_EXPRESS_EXTENDED_END_TIME`
          2) `{ALIAS}_{MEAL}_EXPRESS_WINDOW_START`, `{ALIAS}_{MEAL}_EXPRESS_EXTENDED_END_TIME`
          3) `{MEAL}_EXPRESS_WINDOW_START`, `{MEAL}_EXPRESS_EXTENDED_END_TIME`
          4) `EXPRESS_WINDOW_START`, `EXPRESS_EXTENDED_END_TIME`
        - Time formats accepted for express window and cutoff include: `HH:mm`, `HH:mm:ss`, `Hmm`/`HHmm`, `h A`/`hh:mm A` (e.g., "7 PM", "07:15 PM"), and common variants like `10.30` or `10-30`.
        - If only an express end time is configured, the start defaults to the cutoff time for that meal/day.
        - Breakfast behavior: breakfast menus do not assume non-zero express charges unless configured.
        
        **Express Charge Lookup (when express window applies):**
        - Express extra delivery charge keys are resolved in this order (first match wins):
          1) `K{kitchenCode}_{MEAL}_EXPRESS_EXTRA_DELIVERY_CHARGE`
          2) `{ALIAS}_{MEAL}_EXPRESS_EXTRA_DELIVERY_CHARGE`
          3) `{MEAL}_EXPRESS_EXTRA_DELIVERY_CHARGE`
          4) `EXPRESS_EXTRA_DELIVERY_CHARGE`
        - Breakfast-specific keys (e.g., `K1_BREAKFAST_EXPRESS_EXTRA_DELIVERY_CHARGE`) are supported. If no key is found for breakfast, the express charge defaults to 0.
        - For lunch/other meals, if none of the above keys are present, a legacy fallback may apply (keys `c` or `C`, if configured).
      operationId: getProductMenu
      tags: [Product Menu]
      parameters:
        - name: company_id
          in: query
          required: true
          schema:
            type: integer
            minimum: 1
            maximum: 99999
          description: Company identifier to scope data
          example: 8163
        - name: city
          in: query
          required: false
          schema:
            type: string
            maxLength: 100
          description: City name to filter delivery locations
          example: "Mumbai"
        - name: delivery_location
          in: query
          required: false
          schema:
            type: string
            maxLength: 255
          description: Delivery location name
        - name: delivery_location_id
          in: query
          required: false
          schema:
            type: integer
            minimum: 1
          description: Exact delivery location ID
          example: 9
        - name: from_date
          in: query
          required: false
          schema:
            type: string
            format: date
          description: Start date (YYYY-MM-DD) for menu cycle filtering
          example: "2025-08-25"
        - name: to_date
          in: query
          required: false
          schema:
            type: string
            format: date
          description: End date (YYYY-MM-DD) for menu cycle filtering
          example: "2025-08-29"
        - name: date
          in: query
          required: false
          schema:
            type: string
            format: date
          description: |
            Specific date (YYYY-MM-DD) to fetch menu items for that day. If omitted, server uses current date.
            When provided without from_date/to_date, menu_items reflect this date and menu_items_by_date remains null.
          example: "2025-08-18"
      responses:
        '200':
          description: Product menu retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductMenuResponse'
        '404':
          description: No menu type configured or no active products found
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "No menu type configured for this kitchen or globally"
        '422':
          description: Validation failed (e.g., missing or invalid company_id)
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Validation failed
                  errors:
                    type: object
                    additionalProperties: true
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/delivery-locations:
    get:
      summary: Get delivery locations
      description: |
        Retrieve a paginated list of delivery locations (schools) filtered by company.
        Supports filtering by status, city, and search functionality.

        **Features:**
        - Company-scoped filtering (automatically filtered by company_id)
        - Status filtering (active/inactive)
        - City-based filtering
        - Search by location name
      operationId: getDeliveryLocations
      tags: [Delivery Locations]
      
      parameters:
        - name: company_id
          in: query
          required: true
          schema:
            type: integer
            minimum: 1
            maximum: 99999
          description: Company identifier to scope data
          example: 8163
        - name: per_page
          in: query
          required: false
          schema:
            type: integer
            default: 15
            minimum: 1
            maximum: 100
          description: Number of items per page
          example: 15
        - name: status
          in: query
          required: false
          schema:
            type: integer
            enum: [0, 1]
          description: Filter by status (0=Inactive, 1=Active)
          example: 1
        - name: search
          in: query
          required: false
          schema:
            type: string
          description: Search in location name
        - name: city
          in: query
          required: false
          schema:
            type: string
          description: Filter by city
          example: 9
      responses:
        '200':
          description: Delivery locations retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeliveryLocationListResponse'
        '422':
          description: Validation failed (e.g., missing or invalid company_id)
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Validation failed
                  errors:
                    type: object
                    additionalProperties: true
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/delivery-locations/{id}:
    get:
      summary: Get specific delivery location
      description: |
        Retrieve detailed information about a specific delivery location.
        Automatically filtered by company_id for security.
      operationId: getDeliveryLocationById
      tags: [Delivery Locations]
      parameters:
        - name: company_id
          in: query
          required: true
          schema:
            type: integer
            minimum: 1
            maximum: 99999
          description: Company identifier to scope data
          example: 8163
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Delivery location ID
          example: 2
      responses:
        '200':
          description: Delivery location retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeliveryLocationResponse'
        '404':
          description: Delivery location not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Delivery location not found"
        '422':
          description: Validation failed (e.g., missing or invalid company_id)
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Validation failed
                  errors:
                    type: object
                    additionalProperties: true
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/customers/{id}/wallet:
    get:
      summary: Get customer wallet details
      description: |
        Returns the wallet balance and details for a specific customer with enhanced order status-based calculation.

        **Enhanced Balance Calculation:**
        - **Available Balance** = Total Credits - Total Debits - Locked Amounts - Pending Order Amounts
        - **Pending Orders** (New, Confirmed, Processing) are counted as locked amounts
        - **Delivered Orders** (Complete, Delivered) are counted as deducted amounts
        - **Cancelled Orders** are automatically excluded from calculations (no wallet entries created)

        **Key Features:**
        - ✅ Real-time balance calculation based on order status
        - ✅ No wallet entries for cancelled orders (cleaner transaction history)
        - ✅ Automatic wallet deduction when orders are delivered
        - ✅ Prevents double-spending with order status validation
      operationId: getCustomerWallet
      tags: [Wallet Management]
      parameters:
        - name: id
          in: path
          required: true
          description: Customer ID
          schema:
            type: integer
            example: 1543
        - name: company_id
          in: query
          required: true
          description: Company ID for scoping and validation
          schema:
            type: integer
            minimum: 1
            example: 1
      responses:
        '200':
          description: Wallet details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/WalletDetails'
        '404':
          description: Resource not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []

  /v2/customers/{id}/wallet/balance:
    get:
      summary: Get customer wallet balance
      description: |
        Returns only the balance information for a specific customer's wallet with enhanced calculation logic.

        **Enhanced Balance Calculation:**
        - Considers order status when calculating available balance
        - Pending orders (New, Confirmed, Processing) reduce available balance
        - Delivered orders (Complete, Delivered) are automatically deducted
        - Cancelled orders don't affect balance (no wallet entries created)

        **Response includes:**
        - Available balance (what customer can spend)
        - Total credits, debits, and locked amounts
        - Pending and delivered order amounts for transparency
      operationId: getCustomerWalletBalance
      tags: [Wallet Management]
      parameters:
        - name: id
          in: path
          required: true
          description: Customer ID
          schema:
            type: integer
            example: 1543
        - name: company_id
          in: query
          required: true
          description: Company ID for scoping and validation
          schema:
            type: integer
            minimum: 1
            example: 1
      responses:
        '200':
          description: Wallet balance retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      customer_id:
                        type: integer
                        example: 1543
                      balance:
                        type: number
                        format: float
                        example: 367.50
                        description: Available balance in the wallet
                      currency:
                        type: string
                        example: INR
        '404':
          description: Resource not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []

  /v2/customers/{id}/wallet/deposit:
    post:
      summary: Deposit money to customer wallet
      description: Adds money to a customer's wallet
      operationId: depositToCustomerWallet
      tags: [Wallet Management]
      parameters:
        - name: id
          in: path
          required: true
          description: Customer ID
          schema:
            type: integer
            example: 1543
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [company_id, amount]
              properties:
                company_id:
                  type: integer
                  minimum: 1
                  example: 1
                  description: Company ID for scoping and validation
                amount:
                  type: number
                  format: float
                  minimum: 0.01
                  example: 500.00
                  description: Amount to deposit
                description:
                  type: string
                  example: "Wallet recharge via online payment"
                  description: Description for the deposit transaction
                transaction_id:
                  type: string
                  example: "TXN*********"
                  description: External transaction reference ID
      responses:
        '200':
          description: Deposit successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Deposit successful"
                  data:
                    $ref: '#/components/schemas/WalletDetails'
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Resource not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []

  /v2/customers/{id}/wallet/withdraw:
    post:
      summary: Withdraw money from customer wallet
      description: Withdraws money from a customer's wallet
      operationId: withdrawFromCustomerWallet
      tags: [Wallet Management]
      parameters:
        - name: id
          in: path
          required: true
          description: Customer ID
          schema:
            type: integer
            example: 1543
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [company_id, amount]
              properties:
                company_id:
                  type: integer
                  minimum: 1
                  example: 1
                  description: Company ID for scoping and validation
                amount:
                  type: number
                  format: float
                  minimum: 0.01
                  example: 100.00
                  description: Amount to withdraw
                description:
                  type: string
                  example: "Order payment deduction"
                  description: Description for the withdrawal transaction
                transaction_id:
                  type: string
                  example: "ORDER123456"
                  description: External transaction reference ID
      responses:
        '200':
          description: Withdrawal successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Withdrawal successful"
                  data:
                    $ref: '#/components/schemas/WalletDetails'
        '400':
          description: Insufficient balance or validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Insufficient balance for withdrawal. Available: ₹367.50, Requested: ₹500.00"
        '404':
          description: Resource not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []

  /v2/customers/{id}/wallet/transactions:
    get:
      summary: Get customer wallet transaction history
      description: Returns paginated transaction history for a customer's wallet
      operationId: getCustomerWalletTransactions
      tags: [Wallet Management]
      parameters:
        - name: id
          in: path
          required: true
          description: Customer ID
          schema:
            type: integer
            example: 1543
        - name: company_id
          in: query
          required: true
          description: Company ID for scoping and validation
          schema:
            type: integer
            minimum: 1
            example: 1
        - name: type
          in: query
          required: false
          description: Filter by transaction type
          schema:
            type: string
            enum: [credit, debit, lock]
            example: credit
        - name: date_from
          in: query
          required: false
          description: Filter transactions from this date
          schema:
            type: string
            format: date
            example: "2025-07-01"
        - name: date_to
          in: query
          required: false
          description: Filter transactions until this date
          schema:
            type: string
            format: date
            example: "2025-07-11"
        - name: per_page
          in: query
          required: false
          description: Number of transactions per page
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 15
            example: 15
        - name: page
          in: query
          required: false
          description: Page number
          schema:
            type: integer
            minimum: 1
            default: 1
            example: 1
      responses:
        '200':
          description: Transaction history retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/WalletTransaction'
                      current_page:
                        type: integer
                        example: 1
                      total:
                        type: integer
                        example: 50
                      per_page:
                        type: integer
                        example: 15
        '404':
          description: Resource not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []

  /v2/customer-addresses:
    post:
      summary: Create customer address (JWT Protected)
      description: Create a new customer address (student profile for school menu).
      operationId: createCustomerAddressJWT
      tags: [Customer Address]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required: [customer_id, company_id, location_code, child_name, class, division, floor]
              properties:
                customer_id:
                  type: integer
                company_id:
                  type: integer
                  minimum: 1
                  maximum: 99999
                location_code:
                  type: integer
                child_name:
                  type: string
                class:
                  type: string
                division:
                  type: string
                floor:
                  type: string
                allergies:
                  type: array
                  items:
                    type: string
                menu_type:
                  type: string
                  enum: [school]
                city:
                  type: string
                default:
                  type: boolean
      responses:
        '201':
          description: Customer address created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerAddressResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []

  /v2/customer-addresses/{id}:
    put:
      summary: Update customer address (JWT Protected)
      description: Update a customer address by ID.
      operationId: updateCustomerAddressJWT
      tags: [Customer Address]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required: [customer_id, company_id]
              properties:
                customer_id:
                  type: integer
                company_id:
                  type: integer
                  minimum: 1
                  maximum: 99999
                location_code:
                  type: integer
                child_name:
                  type: string
                class:
                  type: string
                division:
                  type: string
                floor:
                  type: string
                allergies:
                  type: array
                  items:
                    type: string
                default:
                  type: boolean
      responses:
        '200':
          description: Customer address updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerAddressResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Customer address not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []

    delete:
      summary: Delete customer address (JWT Protected)
      description: Delete a customer address by ID.
      operationId: deleteCustomerAddressJWT
      tags: [Customer Address]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
        - name: customer_id
          in: query
          schema:
            type: integer
        - name: company_id
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 99999
      responses:
        '200':
          description: Customer address deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Customer address not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []

  /v2/customer-addresses/{id}/set-default:
    patch:
      summary: Set default customer address (JWT Protected)
      description: Set the specified address as default for the customer, scoped by company.
      operationId: setDefaultCustomerAddressJWT
      tags: [Customer Address]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required: [customer_id, company_id]
              properties:
                customer_id:
                  type: integer
                company_id:
                  type: integer
                  minimum: 1
                  maximum: 99999
      responses:
        '200':
          description: Default address updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerAddressResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Customer address not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []

  /v2/customer-addresses/selected:
    get:
      summary: Get selected customer address (JWT Protected)
      description: Returns the currently selected (default) address for the customer, scoped by company.
      operationId: getSelectedCustomerAddressJWT
      tags: [Customer Address]
      parameters:
        - name: customer_id
          in: query
          schema:
            type: integer
        - name: company_id
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 99999
      responses:
        '200':
          description: Selected address fetched successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerAddressResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []

  /v2/wallet/{customerId}:
    get:
      summary: Get customer wallet details (Direct Access)
      description: |
        Returns the wallet balance and details for a specific customer via direct wallet endpoint with enhanced order status-based calculation.

        **Enhanced Balance Calculation:**
        - **Available Balance** = Total Credits - Total Debits - Locked Amounts - Pending Order Amounts
        - **Pending Orders** (New, Confirmed, Processing) are counted as locked amounts
        - **Delivered Orders** (Complete, Delivered) are counted as deducted amounts
        - **Cancelled Orders** are automatically excluded from calculations

        **Key Features:**
        - ✅ Real-time balance calculation based on order status
        - ✅ No wallet entries for cancelled orders
        - ✅ Automatic wallet deduction when orders are delivered
        - ✅ Direct access endpoint for internal service communication
      operationId: getCustomerWalletDirect
      tags: [Wallet Management]
      parameters:
        - name: customerId
          in: path
          required: true
          description: Customer ID
          schema:
            type: integer
        - name: company_id
          in: query
          description: Company ID for scoping and validation
          schema:
            type: integer
            minimum: 1
      responses:
        '200':
          description: Wallet details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    $ref: '#/components/schemas/WalletDetails'
        '404':
          description: Resource not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []

  /v2/wallet/{customerId}/balance:
    get:
      summary: Get customer wallet balance (Direct Access)
      description: |
        Returns only the balance information for a specific customer's wallet via direct wallet endpoint with enhanced calculation logic.

        **Enhanced Balance Calculation:**
        - Considers order status when calculating available balance
        - Pending orders (New, Confirmed, Processing) reduce available balance
        - Delivered orders (Complete, Delivered) are automatically deducted
        - Cancelled orders don't affect balance (no wallet entries created)

        **Direct Access Features:**
        - Optimized for internal service-to-service communication
        - Bypasses customer authentication for system operations
        - Returns comprehensive balance breakdown for debugging
      operationId: getCustomerWalletBalanceDirect
      tags: [Wallet Management]
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
        - name: company_id
          in: query
          schema:
            type: integer
            minimum: 1
      responses:
        '200':
          description: Wallet balance retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    type: object
                    properties:
                      customer_id:
                        type: integer
                      balance:
                        type: number
                        format: float
                      currency:
                        type: string
        '404':
          description: Resource not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []

  /v2/wallet/{customerId}/transactions:
    get:
      summary: Get customer wallet transaction history (Direct Access)
      description: Returns paginated transaction history for a customer's wallet via direct wallet endpoint
      operationId: getCustomerWalletTransactionsDirect
      tags: [Wallet Management]
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
        - name: company_id
          in: query
          schema:
            type: integer
            minimum: 1
        - name: type
          in: query
          schema:
            type: string
            enum: [credit, debit, lock]
        - name: date_from
          in: query
          schema:
            type: string
            format: date
        - name: date_to
          in: query
          schema:
            type: string
            format: date
        - name: per_page
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 15
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
      responses:
        '200':
          description: Transaction history retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WalletTransactionsResponse'
        '404':
          description: Resource not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []

  /v2/wallet/{customerId}/details:
    get:
      summary: Get wallet details with history (UI)
      description: Returns wallet details and paginated history ordered by timestamp desc
      operationId: getWalletWithHistoryDirect
      tags: [Wallet Management]
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
        - name: company_id
          in: query
          schema:
            type: integer
            minimum: 1
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: per_page
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 15
        - name: date_from
          in: query
          schema:
            type: string
            format: date
        - name: date_to
          in: query
          schema:
            type: string
            format: date
      responses:
        '200':
          description: Wallet details with history retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WalletWithHistoryResponse'
        '404':
          description: Resource not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []

  /v2/wallet/add:
    post:
      summary: Add funds to wallet (JWT Protected)
      description: Initiate wallet top-up by creating a payment request.
      operationId: addWalletFundsJWT
      tags: [Wallet Management]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required: [customer_id, amount, company_id]
              properties:
                customer_id:
                  type: integer
                amount:
                  type: number
                  format: float
                  minimum: 1
                company_id:
                  type: integer
                  minimum: 1
                  maximum: 99999
                transaction_id:
                  type: string
                  nullable: true
                payment_method:
                  type: string
                  enum: [razorpay, payu, manual]
                  nullable: true
                description:
                  type: string
                  maxLength: 255
                  nullable: true
      responses:
        '202':
          description: Payment initiated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      payment_id:
                        type: string
                      status:
                        type: string
                        example: pending
                      reference:
                        type: string
                      payment_urls:
                        type: object
                        properties:
                          process_payment:
                            type: string
                          payment_status:
                            type: string
                      amount:
                        type: number
                      currency:
                        type: string
                        example: INR
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []

  /v2/wallet/deduct:
    post:
      summary: Deduct funds from wallet (JWT Protected)
      description: Deduct funds from customer wallet for order payments or other transactions.
      operationId: deductWalletFundsJWT
      tags: [Wallet Management]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required: [customer_id, amount, company_id, description]
              properties:
                customer_id:
                  type: integer
                amount:
                  type: number
                  format: float
                  minimum: 1
                company_id:
                  type: integer
                  minimum: 1
                  maximum: 99999
                description:
                  type: string
                  maxLength: 255
                order_id:
                  type: string
                  nullable: true
                reference_id:
                  type: string
                  nullable: true
      responses:
        '200':
          description: Withdrawal successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    $ref: '#/components/schemas/WalletDetails'
        '400':
          description: Insufficient balance
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []

  /v2/wallet/transfer:
    post:
      summary: Transfer funds between wallets (JWT Protected)
      description: Transfer funds from one customer wallet to another within the same company.
      operationId: transferWalletFundsJWT
      tags: [Wallet Management]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required: [from_customer_id, to_customer_id, amount, company_id, description]
              properties:
                from_customer_id:
                  type: integer
                to_customer_id:
                  type: integer
                amount:
                  type: number
                  format: float
                  minimum: 1
                company_id:
                  type: integer
                  minimum: 1
                  maximum: 99999
                description:
                  type: string
                  maxLength: 255
                reference_id:
                  type: string
                  nullable: true
      responses:
        '200':
          description: Transfer completed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      transfer_id:
                        type: integer
                      from_balance:
                        type: number
                      to_balance:
                        type: number
                      amount_transferred:
                        type: number
        '400':
          description: Transfer failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []

  /v2/wallet/history:
    get:
      summary: Get all wallet history (JWT Protected)
      description: Returns global wallet history (admin/reporting scope). Implementation may restrict usage.
      operationId: getAllWalletHistory
      tags: [Wallet Management]
      parameters:
        - name: company_id
          in: query
          schema:
            type: integer
            minimum: 1
        - name: per_page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 15
      responses:
        '200':
          description: History retrieved
          content:
            application/json:
              schema:
                type: object
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []

  /v2/wallet/statistics:
    get:
      summary: Get wallet statistics (JWT Protected)
      description: Returns aggregate wallet statistics (admin/reporting scope).
      operationId: getWalletStatistics
      tags: [Wallet Management]
      parameters:
        - name: company_id
          in: query
          schema:
            type: integer
            minimum: 1
      responses:
        '200':
          description: Statistics retrieved
          content:
            application/json:
              schema:
                type: object
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []

  /v2/wallet/payments/callback:
    post:
      summary: Wallet payment callback (JWT Protected)
      description: Unified callback from payment-service for wallet top-ups.
      operationId: walletPaymentCallback
      tags: [Wallet Management]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required: [status, reference, payment_id, metadata]
              properties:
                status:
                  type: string
                  enum: [success, failure]
                reference:
                  type: string
                payment_id:
                  type: string
                gateway_txn_id:
                  type: string
                  nullable: true
                metadata:
                  type: object
                  required: [customer_id, company_id]
                  properties:
                    customer_id:
                      type: integer
                    company_id:
                      type: integer
      responses:
        '200':
          description: Callback processed
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    $ref: '#/components/schemas/WalletDetails'
      security:
        - bearerAuth: []

