<?php

namespace App\Services;

use App\Events\Wallet\WalletDeposited;
use App\Events\Wallet\WalletWithdrawn;
use App\Exceptions\Wallet\InsufficientBalanceException;
use App\Exceptions\Wallet\WalletNotFoundException;
use App\Models\Customer;
use App\Models\CustomerWallet;
use App\Models\WalletTransaction;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Psr\Log\LoggerInterface;
use Exception;

/**
 * Wallet Service
 *
 * This service encapsulates all wallet-related business logic.
 */
class WalletService
{
    /**
     * Create a new WalletService instance.
     */
    public function __construct(
        protected Dispatcher $events,
        protected LoggerInterface $logger,
        protected CustomerService $customerService
    ) {
    }

    /**
     * Get a customer's wallet balance and details
     *
     * @param int $customerId
     * @return array
     * @throws WalletNotFoundException
     */
    public function getWallet(int $customerId): array
    {
        try {
            // Ensure customer exists (throws if not)
            $this->customerService->getCustomerById($customerId);

            // 1) Usable = sum of credit entries (cr) in customer_wallet + cancelled order amounts
            $totalCredit = $this->getTotalCredit($customerId);
            $cancelledOrderAmount = $this->getCancelledOrdersAmount($customerId);
            $usableBalance = $totalCredit + $cancelledOrderAmount;

            // 2) Locked = pending orders (order_status = 'Pending' AND delivery_status = 'Pending') amount (formula)
            $lockedBalance = $this->getPendingLockedOrdersAmount($customerId);

            // 3) Available = usable + locked
            $availableBalance = $usableBalance + $lockedBalance;

            // For backward compatibility also compute total debit (might be used elsewhere)
            $totalDebit = $this->getTotalDebit($customerId);

            return [
                'customer_id' => $customerId,
                'customer_code' => $customerId,
                'balance' => $availableBalance, // exposed balance = available
                'available_balance' => $availableBalance,
                'usable_balance' => $usableBalance,
                'locked_balance' => $lockedBalance,
                'total_credit' => $totalCredit,
                'total_debit' => $totalDebit,
                'cancelled_order_amount' => $cancelledOrderAmount,
                'currency' => 'INR',
                'status' => 'active',
                'wallet_details' => [
                    'available_balance' => $availableBalance,
                    'usable_balance' => $usableBalance,
                    'locked_balance' => $lockedBalance,
                    'cancelled_order_amount' => $cancelledOrderAmount
                ]
            ];
        } catch (Exception $e) {
            $this->logger->error('Error getting wallet', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'customer_id' => $customerId
            ]);

            throw $e;
        }
    }

    /**
     * Get wallet balance for a customer
     *
     * @param int $customerId
     * @return float
     */
    public function getBalance(int $customerId): float
    {
        $walletData = $this->getWallet($customerId);
        return $walletData['balance'];
    }

    /**
     * Create a pending lock entry for a wallet top-up intent
     *
     * @param int $customerId
     * @param float $amount
     * @param string $reference
     * @param string $description
     * @param int $companyId Optional company id to use for the record
     */
    public function createPendingLock(int $customerId, float $amount, string $reference, string $description = '', int $companyId = 0): void
    {
        $company = $companyId ?: (int) (config('app.company_id') ?? 8163);

        DB::table('customer_wallet')->insert([
            'fk_customer_code' => $customerId,
            'company_id'    => $company,
            'unit_id'       => $company,
            'wallet_amount' => $amount,
            'amount_type' => 'lock',
            'description' => $description ?: 'Wallet top-up pending payment',
            'reference_no' => $reference,
            'payment_date' => now()->toDateString(),
            'created_by' => 1,
            'created_date' => now(),
            'updated_by' => 1,
            'updated_date' => now(),
            'context' => 'customer',
            'payment_type' => 'online'
        ]);
    }

    /**
     * On successful payment, convert lock to credit (idempotent)
     *
     * @param int $customerId
     * @param string $reference Reference used when creating lock (or payment_id)
     * @param string|null $gatewayTxnId
     * @param int $companyId Optional company id to use for the credit record
     */
    public function settleTopupSuccess(int $customerId, string $reference, ?string $gatewayTxnId = null, int $companyId = 0): array
    {
    $company = $companyId ?: (int) (config('app.company_id') ?? 8163);

    return DB::transaction(function () use ($customerId, $reference, $company, $gatewayTxnId) {
            $lock = DB::table('customer_wallet')
                ->where('fk_customer_code', $customerId)
                ->where('reference_no', $reference)
                ->where('amount_type', 'lock')
                ->first();

            if (!$lock) {
                // Already processed or no lock; ensure not already credited
                $alreadyCredited = DB::table('customer_wallet')
                    ->where('fk_customer_code', $customerId)
                    ->where('reference_no', $reference)
                    ->where('amount_type', 'cr')
                    ->exists();
                if ($alreadyCredited) {
                    return $this->getWallet($customerId);
                }
                // If neither lock nor credit exists, nothing to do
                return $this->getWallet($customerId);
            }

            // Remove lock
            DB::table('customer_wallet')
                ->where('customer_wallet_id', $lock->customer_wallet_id)
                ->delete();

            // Add credit (append gateway and company info if available for traceability)
            $desc = 'Wallet top-up successful';
            if ($gatewayTxnId) {
                $desc .= ' (gateway: ' . $gatewayTxnId . ')';
            }
            $desc .= ' [company:' . $company . ']';

            DB::table('customer_wallet')->insert([
                'fk_customer_code' => $customerId,
                'company_id'    => $company,
                'unit_id'       => $company,
                'wallet_amount' => (float) $lock->wallet_amount,
                'amount_type' => 'cr',
                'description' => $desc,
                'reference_no' => $reference,
                'payment_date' => now()->toDateString(),
                'created_by' => $customerId,
                'created_date' => now(),
                'updated_by' => $customerId,
                'updated_date' => now(),
                'context' => 'customer',
                'payment_type' => 'online',
            ]);

            return $this->getWallet($customerId);
        });
    }

    /**
     * On failed payment, remove lock (idempotent)
     */
    public function settleTopupFailure(int $customerId, string $reference, int $companyId = 0): void
    {
        // Company id not required to remove locks, keep for compatibility
        DB::table('customer_wallet')
            ->where('fk_customer_code', $customerId)
            ->where('reference_no', $reference)
            ->where('amount_type', 'lock')
            ->delete();

        Log::error('Payment failed while adding wallet amount', [
            'customer_id' => $customerId,
            'reference' => $reference,
            'company_id' => $companyId
        ]);
    }

    /**
     * Deposit to a customer's wallet
     *
     * @param int $customerId
     * @param float $amount
     * @param string $description
     * @param string $transactionId
     * @param int $companyId Optional company id used for the record
     * @return array
     * @throws WalletNotFoundException
     */
    public function deposit(int $customerId, float $amount, string $description = '', string $transactionId = '', int $companyId = 0): array
    {
        try {
            // Validate amount
            if ($amount <= 0) {
                throw new \InvalidArgumentException("Deposit amount must be greater than zero");
            }

            // Ensure customer exists
            $this->customerService->getCustomerById($customerId);

            // Determine company/unit
            $company = $companyId ?: (int) (config('app.company_id') ?? 8163);

            // Start transaction
            DB::beginTransaction();

            // Get current balance
            $currentWallet = $this->getWallet($customerId);
            $oldBalance = $currentWallet['balance'];

            // Insert credit transaction
            DB::table('customer_wallet')->insert([
                'company_id'    => $company,
                'unit_id'       => $company,
                'fk_customer_code' => $customerId,
                'wallet_amount' => $amount,
                'amount_type' => 'cr',
                'reference_no' => $transactionId ?: uniqid('dep_'),
                'payment_date' => now()->toDateString(),
                'description' => $description ?: 'Wallet deposit',
                'created_by' => 1, // System user
                'created_date' => now(),
                'updated_by' => 1,
                'updated_date' => now(),
                'context' => 'customer',
                'payment_type' => 'online'
            ]);

            // Commit transaction
            DB::commit();

            // Get updated wallet
            $updatedWallet = $this->getWallet($customerId);

            // Log
            $this->logger->info('Wallet deposit', [
                'customer_id' => $customerId,
                'amount' => $amount,
                'old_balance' => $oldBalance,
                'new_balance' => $updatedWallet['balance'],
                'transaction_id' => $transactionId
            ]);

            return $updatedWallet;
        } catch (Exception $e) {
            // Rollback transaction
            DB::rollBack();

            // Log error
            $this->logger->error('Error depositing to wallet', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'customer_id' => $customerId,
                'amount' => $amount
            ]);

            throw $e;
        }
    }

    /**
     * Withdraw from a customer's wallet
     *
     * @param int $customerId
     * @param float $amount
     * @param string $description
     * @param string $transactionId
     * @param int $companyId Optional company id used for the record
     * @return array
     * @throws WalletNotFoundException
     * @throws InsufficientBalanceException
     */
    public function withdraw(int $customerId, float $amount, string $description = '', string $transactionId = '', int $companyId = 0): array
    {
        try {
            // Validate amount
            if ($amount <= 0) {
                throw new \InvalidArgumentException("Withdrawal amount must be greater than zero");
            }

            // Ensure customer exists
            $this->customerService->getCustomerById($customerId);

            // Determine company/unit
            $company = $companyId ?: (int) (config('app.company_id') ?? 8163);

            // Start transaction
            DB::beginTransaction();

            // Get current balance
            $currentWallet = $this->getWallet($customerId);
            $oldBalance = $currentWallet['balance'];

            // Check balance
            if ($oldBalance < $amount) {
                throw new InsufficientBalanceException("Insufficient balance for withdrawal. Available: ₹{$oldBalance}, Requested: ₹{$amount}");
            }

            // Insert debit transaction
            DB::table('customer_wallet')->insert([
                'company_id'    => $company,
                'unit_id'       => $company,
                'fk_customer_code' => $customerId,
                'wallet_amount' => $amount,
                'amount_type' => 'dr',
                'reference_no' => $transactionId ?: uniqid('wit_'),
                'payment_date' => now()->toDateString(),
                'description' => $description ?: 'Wallet withdrawal',
                'created_by' => 1, // System user
                'created_date' => now(),
                'updated_by' => 1,
                'updated_date' => now(),
                'context' => 'customer',
                'payment_type' => 'wallet'
            ]);

            // Commit transaction
            DB::commit();

            // Get updated wallet
            $updatedWallet = $this->getWallet($customerId);

            // Log
            $this->logger->info('Wallet withdrawal', [
                'customer_id' => $customerId,
                'amount' => $amount,
                'old_balance' => $oldBalance,
                'new_balance' => $updatedWallet['balance'],
                'transaction_id' => $transactionId
            ]);

            return $updatedWallet;
        } catch (Exception $e) {
            // Rollback transaction
            DB::rollBack();

            // Log error
            $this->logger->error('Error withdrawing from wallet', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'customer_id' => $customerId,
                'amount' => $amount
            ]);

            throw $e;
        }
    }

    /**
     * Get wallet transaction history
     *
     * @param int $customerId
     * @param array $filters
     * @param int $perPage
     * @param int|null $page
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getTransactionHistory(int $customerId, array $filters = [], int $perPage = 15, ?int $page = null)
    {
        $query = DB::table('customer_wallet')
            ->where('fk_customer_code', $customerId)
            ->select([
                'customer_wallet_id as id',
                'fk_customer_code as customer_code',
                'wallet_amount as amount',
                'amount_type as type',
                'description',
                'reference_no as transaction_id',
                'payment_date as date',
                'payment_type',
                'context',
                'created_date as created_at',
                'updated_date as updated_at'
            ]);

        // Apply filters
        if (isset($filters['type'])) {
            // Map API type to database type
            $typeMap = [
                'deposit' => 'cr',
                'credit' => 'cr',
                'withdrawal' => 'dr',
                'debit' => 'dr',
                'lock' => 'lock'
            ];
            $dbType = $typeMap[$filters['type']] ?? $filters['type'];
            $query->where('amount_type', $dbType);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('created_date', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_date', '<=', $filters['date_to']);
        }

        // Order by
        $query->orderBy('created_date', 'desc');

        // Get paginated results
        $total = $query->count();

        // Determine page (explicit parameter takes precedence, fallback to request)
        $page = $page ?? (int) request('page', 1);
        $page = max(1, (int)$page);

        $transactions = $query->offset(($perPage * ($page - 1)))->limit($perPage)->get();

        // Transform the data
        $transformedTransactions = $transactions->map(function ($transaction) {
            return [
                'id' => $transaction->id,
                'customer_code' => $transaction->customer_code,
                'amount' => (float) $transaction->amount,
                'type' => $this->mapDbTypeToApiType($transaction->type),
                'description' => $transaction->description,
                'transaction_id' => $transaction->transaction_id,
                'date' => $transaction->date,
                'payment_type' => $transaction->payment_type,
                'context' => $transaction->context,
                'created_at' => $transaction->created_at,
                'updated_at' => $transaction->updated_at
            ];
        });

        return new \Illuminate\Pagination\LengthAwarePaginator(
            $transformedTransactions,
            $total,
            $perPage,
            $page,
            ['path' => request()->url()]
        );
    }

    /**
     * Get wallet details with transaction history in UI format
     *
     * @param int $customerId
     * @param array $filters
     * @param int $perPage
     * @param int $page
     * @return array
     */
    public function getWalletWithHistory(int $customerId, array $filters = [], int $perPage = 15, int $page = 1): array
    {
        // Get wallet details
        $walletDetails = $this->getWallet($customerId);

        // Get transaction history
        $transactionHistory = $this->getTransactionHistory($customerId, $filters, $perPage, $page);

        // Transform transactions for UI
        $transformedTransactions = collect($transactionHistory->items())->map(function ($transaction) {
            $creditDebit = match($transaction['type']) {
                'credit' => 'Cr',
                'debit' => 'Dr',
                'lock' => 'Lock',
                default => 'Unknown'
            };

            return [
                'date' => date('d-m-Y', strtotime($transaction['date'])),
                'description' => $transaction['description'],
                'amount' => '₹' . number_format($transaction['amount'], 2),
                'credit_debit' => $creditDebit,
                'payment_mode' => ucfirst($transaction['payment_type'] ?? 'Cash'),
                'transacted_by' => $this->extractTransactedBy($transaction['customer_code'])
            ];
        });

        return [
            'wallet_details' => [
                'available_balance' => $walletDetails['available_balance'],
                'usable_balance' => $walletDetails['usable_balance'],
                'locked_balance' => $walletDetails['locked_balance'],
                'currency' => $walletDetails['currency']
            ],
            'wallet_history' => [
                'data' => $transformedTransactions,
                'pagination' => [
                    'current_page' => $transactionHistory->currentPage(),
                    'per_page' => $transactionHistory->perPage(),
                    'total' => $transactionHistory->total(),
                    'last_page' => $transactionHistory->lastPage()
                ]
            ]
        ];
    }

    /**
     * Extract transacted by from description
     *
     * @param string $description
     * @return string
     */
    private function extractTransactedBy($customerId)
    {
        $customer =  DB::table('customers')
                ->select('customer_name')
                ->where('pk_customer_code', $customerId)
                ->first();
        if($customer){
            return $customer->customer_name;
        }else{
            return 'Admin';
        }
    }

    /**
     * Map database amount_type to API type
     *
     * @param string|null $dbType
     * @return string
     */
    private function mapDbTypeToApiType(?string $dbType): string
    {
        if ($dbType === null) {
            return 'unknown';
        }

        return match ($dbType) {
            'cr' => 'credit',
            'dr' => 'debit',
            'lock' => 'lock',
            default => $dbType
        };
    }

    /* ===================== New Calculation Helpers ===================== */

    /**
     * Sum of all credit (cr) entries for a customer.
     */
    private function getTotalCredit(int $customerId): float
    {
        return (float) DB::table('customer_wallet')
            ->where('fk_customer_code', $customerId)
            ->where('amount_type', 'cr')
            ->sum('wallet_amount');
    }

    /**
     * Sum of all debit (dr) entries – retained for compatibility / insight.
     */
    private function getTotalDebit(int $customerId): float
    {
        return (float) DB::table('customer_wallet')
            ->where('fk_customer_code', $customerId)
            ->where('amount_type', 'dr')
            ->sum('wallet_amount');
    }

    /**
     * Base expression (amount - discount_applied + tax + delivery_charges + service_charges)
     * with COALESCE to avoid NULL arithmetic.
     */
    private function orderValueExpression(): string
    {
    $discountCol = $this->resolveDiscountColumn();
    $taxCol = $this->resolveExistingColumn(['tax','tax_amount']);
    $deliveryCol = $this->resolveExistingColumn(['delivery_charges','delivery_charge','delivery_fee']);
    $serviceCol = $this->resolveExistingColumn(['service_charges','service_charge','service_fee']);
    $c = ',0) + COALESCE(';
    return ' ( COALESCE(amount,0) - COALESCE(' . $discountCol . $c . $taxCol . $c . $deliveryCol . $c . $serviceCol . ',0) ) ';
    }

    /**
     * Cancelled orders amount (orders refunded / released) added back to usable.
     * Includes both possible status strings to be safe.
     */
    private function getCancelledOrdersAmount(int $customerId): float
    {
        $expr = DB::raw('SUM' . $this->wrapInParenthesis($this->orderValueExpression()) . ' as total');

        $row = DB::table('orders')
            ->select($expr)
            ->where('customer_code', $customerId)
            ->whereIn('order_status', ['Cancelled', 'Cancel'])
            ->first();

        return (float) ($row->total ?? 0.0);
    }

    /**
     * Pending (locked) orders amount (order_status = Pending AND delivery_status = Pending).
     * If your actual statuses differ (e.g., 'New'), adjust where clause accordingly.
     */
    private function getPendingLockedOrdersAmount(int $customerId): float
    {
        $expr = DB::raw('SUM' . $this->wrapInParenthesis($this->orderValueExpression()) . ' as total');

        // $lockedStatuses = $this->getLockedStatuses();

        $query = DB::table('orders')
            ->select($expr)
            ->where('customer_code', $customerId)
            ->whereIn('order_status', ['New'])
            ->where('delivery_status', 'Pending');

        $row = $query->first();
        return (float) ($row->total ?? 0.0);
    }

    /**
     * Utility: ensure expression is wrapped once for SUM(...)
     */
    private function wrapInParenthesis(string $expr): string
    {
        $trim = trim($expr);
        if (!str_starts_with($trim, '(')) {
            return '(' . $trim . ')';
        }
        return $trim;
    }

    /**
     * Determine discount column dynamically (prefers discount_applied then applied_discount else returns literal 0).
     */
    private function resolveDiscountColumn(): string
    {
        static $col = null;
        if ($col === null) {
            if (Schema::hasColumn('orders','discount_applied')) {
                $col = 'discount_applied';
            } elseif (Schema::hasColumn('orders','applied_discount')) {
                $col = 'applied_discount';
            } else {
                $col = '0';
            }
        }
        return $col;
    }

    /**
     * Resolve first existing column from candidates; fallback to literal 0 to keep SQL valid.
     */
    private function resolveExistingColumn(array $candidates): string
    {
        static $cache = [];
        $key = implode('|',$candidates);
        if (isset($cache[$key])) { return $cache[$key]; }
        foreach ($candidates as $c) {
            if (Schema::hasColumn('orders',$c)) { $cache[$key] = $c; return $c; }
        }
        $cache[$key] = '0';
        return '0';
    }

    /**
     * Locked statuses list (can be overridden via config wallet.locked_statuses).
     * Includes multiple pre-fulfilment states besides explicit 'Pending'.
     */
    private function getLockedStatuses(): array
    {
        $default = ['Pending','New','Confirmed','Processing'];
        $configured = config('wallet.locked_statuses');
        if (is_array($configured) && count($configured)) {
            return $configured;
        }
        return $default;
    }
}
